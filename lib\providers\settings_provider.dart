import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../repositories/settings_repository.dart';
import '../utils/logger_utils.dart';
import '../utils/provider_exception.dart';
import '../utils/device_utils.dart';
import 'base_async_notifier.dart';
import 'base_state.dart';

/// 설정 상태 클래스
class SettingsState extends BaseState {
  final int eventDayOfWeek;
  final bool collectDayOfWeekFromExcel;
  final int excelDayOfWeekColumnIndex;
  final bool linkPrepaymentToInventory;
  final int inventoryColumns;
  final int saleColumns;
  final int inventoryColumnsPortrait;
  final int inventoryColumnsLandscape;
  final int saleColumnsPortrait;
  final int saleColumnsLandscape;
  final bool isUpdating; // 추가: 업데이트 상태
  final bool isTablet; // 추가: 기기 타입 (태블릿 여부)
  final bool showProductImages; // 추가: POS 상품 이미지 표시 여부

  const SettingsState({
    required this.eventDayOfWeek,
    required this.collectDayOfWeekFromExcel,
    required this.excelDayOfWeekColumnIndex,
    required this.linkPrepaymentToInventory,
    required this.inventoryColumns,
    required this.saleColumns,
    required this.inventoryColumnsPortrait,
    required this.inventoryColumnsLandscape,
    required this.saleColumnsPortrait,
    required this.saleColumnsLandscape,
    required this.isUpdating, // 추가
    required this.isTablet, // 추가: 기기 타입
    this.showProductImages = true, // 추가: 기본값 true (이미지 표시)
    super.isLoading = false,
    super.errorMessage,
    super.errorCode,
    super.errorSeverity,
    super.errorDetails,
    super.isCancelled = false,
  });

  @override
  SettingsState copyWithBase({
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return copyWith(
      isLoading: isLoading,
      errorMessage: errorMessage,
      errorCode: errorCode,
      errorSeverity: errorSeverity,
      errorDetails: errorDetails,
      isCancelled: isCancelled,
    );
  }

  SettingsState copyWith({
    int? eventDayOfWeek,
    bool? collectDayOfWeekFromExcel,
    int? excelDayOfWeekColumnIndex,
    bool? linkPrepaymentToInventory,
    int? inventoryColumns,
    int? saleColumns,
    int? inventoryColumnsPortrait,
    int? inventoryColumnsLandscape,
    int? saleColumnsPortrait,
    int? saleColumnsLandscape,
    bool? isUpdating, // 추가
    bool? isTablet, // 추가: 기기 타입
    bool? showProductImages, // 추가: 상품 이미지 표시 여부
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return SettingsState(
      eventDayOfWeek: eventDayOfWeek ?? this.eventDayOfWeek,
      collectDayOfWeekFromExcel: collectDayOfWeekFromExcel ?? this.collectDayOfWeekFromExcel,
      excelDayOfWeekColumnIndex: excelDayOfWeekColumnIndex ?? this.excelDayOfWeekColumnIndex,
      linkPrepaymentToInventory: linkPrepaymentToInventory ?? this.linkPrepaymentToInventory,
      inventoryColumns: inventoryColumns ?? this.inventoryColumns,
      saleColumns: saleColumns ?? this.saleColumns,
      inventoryColumnsPortrait: inventoryColumnsPortrait ?? this.inventoryColumnsPortrait,
      inventoryColumnsLandscape: inventoryColumnsLandscape ?? this.inventoryColumnsLandscape,
      saleColumnsPortrait: saleColumnsPortrait ?? this.saleColumnsPortrait,
      saleColumnsLandscape: saleColumnsLandscape ?? this.saleColumnsLandscape,
      isUpdating: isUpdating ?? this.isUpdating, // 추가
      isTablet: isTablet ?? this.isTablet, // 추가: 기기 타입
      showProductImages: showProductImages ?? this.showProductImages, // 추가: 상품 이미지 표시 여부
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      errorCode: errorCode ?? this.errorCode,
      errorSeverity: errorSeverity ?? this.errorSeverity,
      errorDetails: errorDetails ?? this.errorDetails,
      isCancelled: isCancelled ?? this.isCancelled,
    );
  }

  @override
  List<Object?> get props => [
    ...super.props,
    eventDayOfWeek,
    collectDayOfWeekFromExcel,
    excelDayOfWeekColumnIndex,
    linkPrepaymentToInventory,
    inventoryColumns,
    saleColumns,
    inventoryColumnsPortrait,
    inventoryColumnsLandscape,
    saleColumnsPortrait,
    saleColumnsLandscape,
    isUpdating, // 추가
    isTablet, // 추가: 기기 타입
    showProductImages, // 추가: POS 상품 이미지 표시 여부
  ];
}

/// Settings Repository Provider
final settingsRepositoryProvider = Provider<SettingsRepository>((ref) {
  return SettingsRepository();
});

/// 앱 설정 상태를 관리하는 Provider입니다.
final settingsNotifierProvider = StateNotifierProvider<SettingsNotifier, AsyncValue<SettingsState>>((ref) {
  return SettingsNotifier(ref);
});

/// 앱 설정 상태 관리를 위한 Notifier 클래스입니다.
/// - 설정 값, CRUD, 상태 변화, 예외 처리 등 모든 비즈니스 로직 담당
class SettingsNotifier extends BaseAsyncNotifier<SettingsState> {
  static const String _tag = 'SettingsNotifier';
  static const String _domain = 'SET';

  bool _isPaused = false;

  SettingsNotifier(super.ref) {
    // 자동 초기화 제거 - AppWrapper에서 순서 제어
    // initialize();
  }



  void pause() {
    if (!_isPaused) {
      _isPaused = true;
      cancelAllOperations();
      LoggerUtils.logDebug('SettingsNotifier paused', tag: _tag);
    }
  }

  void resume([Duration? delay]) {
    if (_isPaused) {
      _isPaused = false;
      LoggerUtils.logDebug('SettingsNotifier resumed', tag: _tag);
      initialize();
    }
  }

  Future<void> initialize() async {
  // 레거시 호환: initialize는 loadSettings 래핑 (향후 제거 가능)
  LoggerUtils.methodStart('initialize(proxy)', tag: _tag);
  await loadSettings();
  LoggerUtils.methodEnd('initialize(proxy)', tag: _tag);
  }

  Future<void> loadSettings({bool showLoading = true}) async {
    LoggerUtils.methodStart('loadSettings', tag: _tag);
    LoggerUtils.logInfo('=== loadSettings 시작 (showLoading: $showLoading) ===', tag: _tag);

    if (showLoading) {
      state = const AsyncValue.loading();
    }

    try {
      final repository = ref.read(settingsRepositoryProvider);
      
      // 타블렛 감지 로직 - 저장된 기기 타입과 실시간 감지 결합
      bool isTablet = false;
      bool isFirstTimeDetection = false;

      try {
        LoggerUtils.logInfo('🔍 === 기기 타입 감지 시작 ===', tag: _tag);

        // 저장된 기기 타입 정보 사용 (UI에서 설정한 값)
        final storedDeviceType = await repository.getBool('device_is_tablet');
        LoggerUtils.logInfo('📋 저장된 기기 타입 확인: $storedDeviceType', tag: _tag);

        if (storedDeviceType != null) {
          // 저장된 값이 있으면 사용
          isTablet = storedDeviceType;
          LoggerUtils.logInfo('💾 저장된 기기 타입 사용: ${isTablet ? "🖥️ 태블릿" : "📱 스마트폰"}', tag: _tag);
        } else {
          // 저장된 값이 없으면 실시간 감지 (앱 최초 실행 시)
          isFirstTimeDetection = true;
          LoggerUtils.logInfo('🆕 최초 실행 감지 - 실시간 기기 타입 감지 시작', tag: _tag);

          isTablet = DeviceUtils.isTabletWithoutContext();
          LoggerUtils.logInfo('🔍 실시간 기기 타입 감지 결과: ${isTablet ? "🖥️ 태블릿" : "📱 스마트폰"}', tag: _tag);

          // 감지 결과를 저장 (다음번에는 저장된 값 사용)
          await repository.setBool('device_is_tablet', isTablet);
          LoggerUtils.logInfo('💾 기기 타입 저장 완료: ${isTablet ? "🖥️ 태블릿" : "📱 스마트폰"}', tag: _tag);

          // 🎯 핵심: 최초 감지 시 즉시 기본값 적용!
          LoggerUtils.logInfo('🎯 최초 감지된 기기 타입에 따른 기본값 적용 시작', tag: _tag);
          await _applyColumnDefaults(repository, isTablet, isPromotion: false);
          LoggerUtils.logInfo('✅ 기본값 적용 완료', tag: _tag);
        }

        LoggerUtils.logInfo('🏁 기기 타입 감지 완료: ${isTablet ? "🖥️ 태블릿" : "📱 스마트폰"}, 최초감지: $isFirstTimeDetection', tag: _tag);
      } catch (e) {
        // 모든 감지 실패 시 스마트폰으로 기본 설정
        isTablet = false;
        LoggerUtils.logError('❌ 기기 타입 정보 처리 실패 → 스마트폰으로 기본 설정', tag: _tag, error: e);
      }
      
      final eventDay = await runCancellableOperation(
        operationName: 'loadEventDay',
        operation: (_) async => await repository.getInt('event_day_of_week') ?? 7,
      );
      LoggerUtils.logInfo('이벤트 요일 로드: $eventDay', tag: _tag);

      // 엑셀 관련 설정 로드
      final collectDayOfWeekFromExcel = await runCancellableOperation(
        operationName: 'loadCollectDayOfWeekFromExcel',
        operation: (_) async => await repository.getBool('collect_day_of_week_from_excel') ?? false,
      );
      LoggerUtils.logInfo('요일 수집 설정 로드: $collectDayOfWeekFromExcel', tag: _tag);

      final excelDayOfWeekColumnIndex = await runCancellableOperation(
        operationName: 'loadExcelDayOfWeekColumnIndex',
        operation: (_) async => await repository.getInt('excel_day_of_week_column_index') ?? -1,
      );
      LoggerUtils.logInfo('요일 열 인덱스 로드: $excelDayOfWeekColumnIndex', tag: _tag);

      final linkPrepaymentToInventory = await runCancellableOperation(
        operationName: 'loadLinkPrepaymentToInventory',
        operation: (_) async => await repository.getBool('link_prepayment_to_inventory') ?? false,
      );
      LoggerUtils.logInfo('재고 연동 설정 로드: $linkPrepaymentToInventory', tag: _tag);

      // UI 열 수 설정 로드 (타블렛 감지에 따른 기본값 적용)
      final inventoryColumns = await runCancellableOperation(
        operationName: 'loadInventoryColumns',
        operation: (_) async => await repository.getInt('inventory_columns') ?? (isTablet ? 6 : 3),
      );
      LoggerUtils.logInfo('재고현황 열 수 로드: $inventoryColumns', tag: _tag);

      final saleColumns = await runCancellableOperation(
        operationName: 'loadSaleColumns',
        operation: (_) async => await repository.getInt('sale_columns') ?? (isTablet ? 6 : 3),
      );
      LoggerUtils.logInfo('판매 화면 열 수 로드: $saleColumns', tag: _tag);

      // 🎯 핵심: 기기 타입에 따른 동적 기본값 적용
      final inventoryColumnsPortrait = await runCancellableOperation(
        operationName: 'loadInventoryColumnsPortrait',
        operation: (_) async => await repository.getInt('inventory_columns_portrait') ?? (isTablet ? 5 : 4),
      );
      LoggerUtils.logInfo('📊 재고현황 세로모드 열 수 로드: $inventoryColumnsPortrait (기기타입: ${isTablet ? "🖥️ 태블릿" : "📱 스마트폰"})', tag: _tag);

      final inventoryColumnsLandscape = await runCancellableOperation(
        operationName: 'loadInventoryColumnsLandscape',
        operation: (_) async => await repository.getInt('inventory_columns_landscape') ?? (isTablet ? 9 : 8),
      );
      LoggerUtils.logInfo('📊 재고현황 가로모드 열 수 로드: $inventoryColumnsLandscape (기기타입: ${isTablet ? "🖥️ 태블릿" : "📱 스마트폰"})', tag: _tag);

      final saleColumnsPortrait = await runCancellableOperation(
        operationName: 'loadSaleColumnsPortrait',
        operation: (_) async => await repository.getInt('sale_columns_portrait') ?? (isTablet ? 5 : 4),
      );
      LoggerUtils.logInfo('📊 판매 화면 세로모드 열 수 로드: $saleColumnsPortrait (기기타입: ${isTablet ? "🖥️ 태블릿" : "📱 스마트폰"})', tag: _tag);

      final saleColumnsLandscape = await runCancellableOperation(
        operationName: 'loadSaleColumnsLandscape',
        operation: (_) async => await repository.getInt('sale_columns_landscape') ?? (isTablet ? 9 : 8),
      );
      LoggerUtils.logInfo('📊 판매 화면 가로모드 열 수 로드: $saleColumnsLandscape (기기타입: ${isTablet ? "🖥️ 태블릿" : "📱 스마트폰"})', tag: _tag);

      // POS 상품 이미지 표시 설정 로드
      final showProductImages = await runCancellableOperation(
        operationName: 'loadShowProductImages',
        operation: (_) async => await repository.getBool('show_product_images') ?? true, // 기본값: true
      );
      LoggerUtils.logInfo('🖼️ POS 상품 이미지 표시 설정 로드: $showProductImages', tag: _tag);

      final newState = SettingsState(
        eventDayOfWeek: eventDay,
        collectDayOfWeekFromExcel: collectDayOfWeekFromExcel,
        excelDayOfWeekColumnIndex: excelDayOfWeekColumnIndex,
        linkPrepaymentToInventory: linkPrepaymentToInventory,
        inventoryColumns: inventoryColumns,
        saleColumns: saleColumns,
        inventoryColumnsPortrait: inventoryColumnsPortrait,
        inventoryColumnsLandscape: inventoryColumnsLandscape,
        saleColumnsPortrait: saleColumnsPortrait,
        saleColumnsLandscape: saleColumnsLandscape,
        isUpdating: false, // 초기값 설정
        isTablet: isTablet, // 기기 타입 설정
        showProductImages: showProductImages, // POS 상품 이미지 표시 설정
      );
      
      LoggerUtils.logInfo('=== SettingsState 구성 완료 ===', tag: _tag);
      LoggerUtils.logInfo('  isTablet: ${newState.isTablet}', tag: _tag);
      LoggerUtils.logInfo('  inventoryColumnsPortrait: ${newState.inventoryColumnsPortrait}', tag: _tag);
      LoggerUtils.logInfo('  inventoryColumnsLandscape: ${newState.inventoryColumnsLandscape}', tag: _tag);
      LoggerUtils.logInfo('  saleColumnsPortrait: ${newState.saleColumnsPortrait}', tag: _tag);
      LoggerUtils.logInfo('  saleColumnsLandscape: ${newState.saleColumnsLandscape}', tag: _tag);
      
      if (mounted) { // mounted 체크 추가
        state = AsyncValue.data(newState);
        LoggerUtils.logInfo('✅ state 업데이트 완료', tag: _tag);
      }
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '설정 로드 중 오류',
        error: e,
        stackTrace: stackTrace,
        tag: _tag,
      );
      if (mounted) { // mounted 체크 추가
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_LOAD_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('loadSettings', tag: _tag);
  }

  Future<void> setEventDayOfWeek(int dayOfWeek) async {
    LoggerUtils.methodStart('setEventDayOfWeek', tag: _tag);

    try {
      if (dayOfWeek < 1 || dayOfWeek > 7) {
        throw ProviderException(
          message: '요일은 1-7 사이의 값이어야 합니다',
          code: '${_domain}_INVALID_DAY',
        );
      }

      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      await runCancellableOperation(
        operationName: 'setEventDay',
        operation: (_) async => await repository.setEventDayOfWeek(dayOfWeek),
      );

      // 값 저장 후 상태 즉시 갱신
      await loadSettings(showLoading: false);
    } catch (e, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_EVENT_DAY_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setEventDayOfWeek', tag: _tag);
  }

  Future<void> deleteSetting(String key) async {
    LoggerUtils.methodStart('deleteSetting', tag: _tag);

    try {
      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      await runCancellableOperation(
        operationName: 'deleteSetting',
        operation: (_) async => await repository.remove(key),
      );

      await loadSettings(showLoading: false);
    } catch (e, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_DELETE_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    } finally {
      if (state.value != null) {
        if (mounted) {
          state = AsyncValue.data(state.value!.copyWith(isUpdating: false));
        }
      }
      LoggerUtils.methodEnd('deleteSetting', tag: _tag);
    }
  }

  // 엑셀 관련 설정 메서드들
  Future<void> setCollectDayOfWeekFromExcel(bool collect) async {
    LoggerUtils.methodStart('setCollectDayOfWeekFromExcel', tag: _tag);
    LoggerUtils.logInfo('요일 수집 설정 변경: $collect', tag: _tag);

    try {
      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setCollectDayOfWeekFromExcel',
        operation: (_) async => await repository.setBool('collect_day_of_week_from_excel', collect),
      );

      if (success) {
        LoggerUtils.logInfo('요일 수집 설정 저장 성공: $collect', tag: _tag);
        // 전체 설정을 다시 로드하여 일관성 보장
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('요일 수집 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '요일 수집 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_COLLECT_DAY_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setCollectDayOfWeekFromExcel', tag: _tag);
  }

  Future<void> setExcelDayOfWeekColumnIndex(int columnIndex) async {
    LoggerUtils.methodStart('setExcelDayOfWeekColumnIndex', tag: _tag);
    LoggerUtils.logInfo('요일 열 인덱스 설정 변경: $columnIndex', tag: _tag);

    try {
      if (columnIndex < -1) {
        throw ProviderException(
          message: '열 인덱스는 -1 이상이어야 합니다',
          code: '${_domain}_INVALID_COLUMN_INDEX',
        );
      }

      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setExcelDayOfWeekColumnIndex',
        operation: (_) async => await repository.setInt('excel_day_of_week_column_index', columnIndex),
      );

      if (success) {
        LoggerUtils.logInfo('요일 열 인덱스 설정 저장 성공: $columnIndex', tag: _tag);
        // 전체 설정을 다시 로드하여 일관성 보장
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('요일 열 인덱스 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '요일 열 인덱스 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_COLUMN_INDEX_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setExcelDayOfWeekColumnIndex', tag: _tag);
  }

  Future<void> setLinkPrepaymentToInventory(bool link) async {
    LoggerUtils.methodStart('setLinkPrepaymentToInventory', tag: _tag);
    LoggerUtils.logInfo('재고 연동 설정 변경: $link', tag: _tag);

    try {
      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setLinkPrepaymentToInventory',
        operation: (_) async => await repository.setBool('link_prepayment_to_inventory', link),
      );

      if (success) {
        LoggerUtils.logInfo('재고 연동 설정 저장 성공: $link', tag: _tag);
        // 전체 설정을 다시 로드하여 일관성 보장
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('재고 연동 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '재고 연동 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_LINK_INVENTORY_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setLinkPrepaymentToInventory', tag: _tag);
  }

  Future<void> setInventoryColumns(int columns) async {
    LoggerUtils.methodStart('setInventoryColumns', tag: _tag);
    LoggerUtils.logInfo('재고현황 열 수 설정 변경: $columns', tag: _tag);

    try {
      if (columns < 3 || columns > 12) {
        throw ProviderException(
          message: '열 수는 3-12 사이의 값이어야 합니다',
          code: '${_domain}_INVALID_COLUMNS',
        );
      }

      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setInventoryColumns',
        operation: (_) async => await repository.setInt('inventory_columns', columns),
      );

      if (success) {
        LoggerUtils.logInfo('재고현황 열 수 설정 저장 성공: $columns', tag: _tag);
        // 전체 설정을 다시 로드하여 일관성 보장
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('재고현황 열 수 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '재고현황 열 수 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_INVENTORY_COLUMNS_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setInventoryColumns', tag: _tag);
  }

  Future<void> setSaleColumns(int columns) async {
    LoggerUtils.methodStart('setSaleColumns', tag: _tag);
    LoggerUtils.logInfo('판매 화면 열 수 설정 변경: $columns', tag: _tag);

    try {
      if (columns < 3 || columns > 12) {
        throw ProviderException(
          message: '열 수는 3-12 사이의 값이어야 합니다',
          code: '${_domain}_INVALID_COLUMNS',
        );
      }

      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setSaleColumns',
        operation: (_) async => await repository.setInt('sale_columns', columns),
      );

      if (success) {
        LoggerUtils.logInfo('판매 화면 열 수 설정 저장 성공: $columns', tag: _tag);
        // 전체 설정을 다시 로드하여 일관성 보장
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('판매 화면 열 수 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '판매 화면 열 수 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_SALE_COLUMNS_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setSaleColumns', tag: _tag);
  }

  Future<void> setInventoryColumnsPortrait(int columns) async {
    LoggerUtils.methodStart('setInventoryColumnsPortrait', tag: _tag);
    LoggerUtils.logInfo('재고현황 세로모드 열 수 설정 변경: $columns', tag: _tag);

    try {
      if (columns < 3 || columns > 12) {
        throw ProviderException(
          message: '열 수는 3-12 사이의 값이어야 합니다',
          code: '${_domain}_INVALID_COLUMNS',
        );
      }

      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setInventoryColumnsPortrait',
        operation: (_) async => await repository.setInt('inventory_columns_portrait', columns),
      );

      if (success) {
        LoggerUtils.logInfo('재고현황 세로모드 열 수 설정 저장 성공: $columns', tag: _tag);
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('재고현황 세로모드 열 수 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '재고현황 세로모드 열 수 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_INVENTORY_COLUMNS_PORTRAIT_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setInventoryColumnsPortrait', tag: _tag);
  }

  Future<void> setInventoryColumnsLandscape(int columns) async {
    LoggerUtils.methodStart('setInventoryColumnsLandscape', tag: _tag);
    LoggerUtils.logInfo('재고현황 가로모드 열 수 설정 변경: $columns', tag: _tag);

    try {
      if (columns < 3 || columns > 12) {
        throw ProviderException(
          message: '열 수는 3-12 사이의 값이어야 합니다',
          code: '${_domain}_INVALID_COLUMNS',
        );
      }

      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setInventoryColumnsLandscape',
        operation: (_) async => await repository.setInt('inventory_columns_landscape', columns),
      );

      if (success) {
        LoggerUtils.logInfo('재고현황 가로모드 열 수 설정 저장 성공: $columns', tag: _tag);
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('재고현황 가로모드 열 수 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '재고현황 가로모드 열 수 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_INVENTORY_COLUMNS_LANDSCAPE_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setInventoryColumnsLandscape', tag: _tag);
  }

  Future<void> setSaleColumnsPortrait(int columns) async {
    LoggerUtils.methodStart('setSaleColumnsPortrait', tag: _tag);
    LoggerUtils.logInfo('판매 화면 세로모드 열 수 설정 변경: $columns', tag: _tag);

    try {
      if (columns < 3 || columns > 12) {
        throw ProviderException(
          message: '열 수는 3-12 사이의 값이어야 합니다',
          code: '${_domain}_INVALID_COLUMNS',
        );
      }

      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setSaleColumnsPortrait',
        operation: (_) async => await repository.setInt('sale_columns_portrait', columns),
      );

      if (success) {
        LoggerUtils.logInfo('판매 화면 세로모드 열 수 설정 저장 성공: $columns', tag: _tag);
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('판매 화면 세로모드 열 수 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '판매 화면 세로모드 열 수 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_SALE_COLUMNS_PORTRAIT_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setSaleColumnsPortrait', tag: _tag);
  }

  Future<void> setSaleColumnsLandscape(int columns) async {
    LoggerUtils.methodStart('setSaleColumnsLandscape', tag: _tag);
    LoggerUtils.logInfo('판매 화면 가로모드 열 수 설정 변경: $columns', tag: _tag);

    try {
      if (columns < 3 || columns > 12) {
        throw ProviderException(
          message: '열 수는 3-12 사이의 값이어야 합니다',
          code: '${_domain}_INVALID_COLUMNS',
        );
      }

      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setSaleColumnsLandscape',
        operation: (_) async => await repository.setInt('sale_columns_landscape', columns),
      );

      if (success) {
        LoggerUtils.logInfo('판매 화면 가로모드 열 수 설정 저장 성공: $columns', tag: _tag);
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('판매 화면 가로모드 열 수 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '판매 화면 가로모드 열 수 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_SALE_COLUMNS_LANDSCAPE_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setSaleColumnsLandscape', tag: _tag);
  }

  /// 기기 타입 설정 (UI에서 화면 크기 기반으로 감지한 결과를 전달받음)
  Future<void> setDeviceType(bool isTablet) async {
    LoggerUtils.methodStart('setDeviceType', tag: _tag);
    LoggerUtils.logInfo('=== 기기 타입 설정 시작 ===', tag: _tag);
    LoggerUtils.logInfo('감지된 기기 타입: ${isTablet ? "태블릿" : "스마트폰"}', tag: _tag);

    try {
      if (!mounted) return;

      final repository = ref.read(settingsRepositoryProvider);

      // 기존 기기 타입 확인
      final previousDeviceType = await repository.getBool('device_is_tablet');
      LoggerUtils.logInfo('이전 기기 타입: ${previousDeviceType == null ? "미설정" : (previousDeviceType ? "태블릿" : "스마트폰")}', tag: _tag);

      // 기기 타입을 저장
      await repository.setBool('device_is_tablet', isTablet);
      LoggerUtils.logInfo('새 기기 타입 저장 완료: ${isTablet ? "태블릿" : "스마트폰"}', tag: _tag);

      // 태블릿 승격 감지 (false -> true)
      final isPromotion = previousDeviceType == false && isTablet == true;
      LoggerUtils.logInfo('승격 여부: $isPromotion (이전: $previousDeviceType → 현재: $isTablet)', tag: _tag);

      // 현재 설정값이 없는 경우 기본값 적용 + 승격 시 스마트폰 기본 패턴이면 태블릿 기본으로 재설정
      await _applyColumnDefaults(repository, isTablet, isPromotion: isPromotion);

      // 설정 다시 로드
      LoggerUtils.logInfo('기기 타입 설정 후 loadSettings 호출', tag: _tag);
      await loadSettings(showLoading: false);

      LoggerUtils.logInfo('=== 기기 타입 설정 완료 ===', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('기기 타입 설정 실패', tag: _tag, error: e, stackTrace: stackTrace);
    } finally {
      LoggerUtils.methodEnd('setDeviceType', tag: _tag);
    }
  }  /// 설정되지 않은 열 수 설정에 대해서만 기본값 적용
  Future<void> _applyColumnDefaults(SettingsRepository repository, bool isTablet, {bool isPromotion = false}) async {
    LoggerUtils.logInfo('🎯 _applyColumnDefaults 시작 - isTablet: $isTablet, isPromotion: $isPromotion', tag: _tag);

    // 현재 값 읽기
    final invP = await repository.getInt('inventory_columns_portrait');
    final invL = await repository.getInt('inventory_columns_landscape');
    final saleP = await repository.getInt('sale_columns_portrait');
    final saleL = await repository.getInt('sale_columns_landscape');

    LoggerUtils.logInfo('📊 현재 저장된 값들:', tag: _tag);
    LoggerUtils.logInfo('  inventory_columns_portrait: $invP', tag: _tag);
    LoggerUtils.logInfo('  inventory_columns_landscape: $invL', tag: _tag);
    LoggerUtils.logInfo('  sale_columns_portrait: $saleP', tag: _tag);
    LoggerUtils.logInfo('  sale_columns_landscape: $saleL', tag: _tag);

    // 스마트폰 기본 패턴 정의
    const phoneInvP = 4;
    const phoneInvL = 8;
    const phoneSaleP = 4;
    const phoneSaleL = 8;

    // 태블릿 기본
    const tabletInvP = 5;
    const tabletInvL = 9;
    const tabletSaleP = 5;
    const tabletSaleL = 9;

    // 승격 조건: 기존 값이 모두 스마트폰 기본 패턴(또는 null) → 사용자 커스텀 아님 판단
    final looksLikePhoneDefaults = (invP == null || invP == phoneInvP) &&
        (invL == null || invL == phoneInvL) &&
        (saleP == null || saleP == phoneSaleP) &&
        (saleL == null || saleL == phoneSaleL);
    
    LoggerUtils.logInfo('스마트폰 기본 패턴 여부: $looksLikePhoneDefaults', tag: _tag);

    if (isPromotion && isTablet && looksLikePhoneDefaults) {
      LoggerUtils.logInfo('🎯 태블릿 승격: 스마트폰 기본 패턴 감지 → 태블릿 기본값으로 재설정', tag: _tag);
      await repository.setInt('inventory_columns_portrait', tabletInvP);
      await repository.setInt('inventory_columns_landscape', tabletInvL);
      await repository.setInt('sale_columns_portrait', tabletSaleP);
      await repository.setInt('sale_columns_landscape', tabletSaleL);
      LoggerUtils.logInfo('태블릿 기본값 설정 완료: portrait=$tabletInvP, landscape=$tabletInvL', tag: _tag);
      return; // 재설정 끝
    }

    // 기존 로직: null 인 값만 채우기
    LoggerUtils.logInfo('기본값 채우기 모드 (null인 값만)', tag: _tag);
    if (invP == null || isPromotion) {
      final newValue = isTablet ? tabletInvP : phoneInvP;
      await repository.setInt('inventory_columns_portrait', newValue);
      LoggerUtils.logInfo('🎯 inventory_columns_portrait 설정: $newValue (${isTablet ? "🖥️ 태블릿" : "📱 스마트폰"})', tag: _tag);

      // 저장 확인
      final savedValue = await repository.getInt('inventory_columns_portrait');
      LoggerUtils.logInfo('✅ inventory_columns_portrait 저장 확인: $savedValue', tag: _tag);
    } else {
      LoggerUtils.logInfo('📋 inventory_columns_portrait 기존값 유지: $invP', tag: _tag);
    }
    if (invL == null || isPromotion) {
      final newValue = isTablet ? tabletInvL : phoneInvL;
      await repository.setInt('inventory_columns_landscape', newValue);
      LoggerUtils.logInfo('🎯 inventory_columns_landscape 설정: $newValue (${isTablet ? "🖥️ 태블릿" : "📱 스마트폰"})', tag: _tag);

      // 저장 확인
      final savedValue = await repository.getInt('inventory_columns_landscape');
      LoggerUtils.logInfo('✅ inventory_columns_landscape 저장 확인: $savedValue', tag: _tag);
    } else {
      LoggerUtils.logInfo('📋 inventory_columns_landscape 기존값 유지: $invL', tag: _tag);
    }
    if (saleP == null || isPromotion) {
      final newValue = isTablet ? tabletSaleP : phoneSaleP;
      await repository.setInt('sale_columns_portrait', newValue);
      LoggerUtils.logInfo('🎯 sale_columns_portrait 설정: $newValue (${isTablet ? "🖥️ 태블릿" : "📱 스마트폰"})', tag: _tag);

      // 저장 확인
      final savedValue = await repository.getInt('sale_columns_portrait');
      LoggerUtils.logInfo('✅ sale_columns_portrait 저장 확인: $savedValue', tag: _tag);
    } else {
      LoggerUtils.logInfo('📋 sale_columns_portrait 기존값 유지: $saleP', tag: _tag);
    }
    if (saleL == null || isPromotion) {
      final newValue = isTablet ? tabletSaleL : phoneSaleL;
      await repository.setInt('sale_columns_landscape', newValue);
      LoggerUtils.logInfo('🎯 sale_columns_landscape 설정: $newValue (${isTablet ? "🖥️ 태블릿" : "📱 스마트폰"})', tag: _tag);

      // 저장 확인
      final savedValue = await repository.getInt('sale_columns_landscape');
      LoggerUtils.logInfo('✅ sale_columns_landscape 저장 확인: $savedValue', tag: _tag);
    } else {
      LoggerUtils.logInfo('📋 sale_columns_landscape 기존값 유지: $saleL', tag: _tag);
    }
    
    LoggerUtils.logInfo('_applyColumnDefaults 완료', tag: _tag);
  }

  /// POS 상품 이미지 표시 여부 설정
  Future<void> setShowProductImages(bool showImages) async {
    LoggerUtils.methodStart('setShowProductImages', tag: _tag);
    LoggerUtils.logInfo('🖼️ POS 상품 이미지 표시 설정 변경: $showImages', tag: _tag);

    try {
      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setShowProductImages',
        operation: (_) async => await repository.setBool('show_product_images', showImages),
      );

      if (success) {
        LoggerUtils.logInfo('🖼️ POS 상품 이미지 표시 설정 저장 성공: $showImages', tag: _tag);
        // 전체 설정을 다시 로드하여 일관성 보장
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('POS 상품 이미지 표시 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          'POS 상품 이미지 표시 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_SHOW_IMAGES_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setShowProductImages', tag: _tag);
  }


}

/// Helper providers
final eventDayOfWeekProvider = Provider<int>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.eventDayOfWeek,
        error: (_, __) => 7, // 기본값: 일요일
        loading: () => 7,
      );
});

final settingsIsUpdatingProvider = Provider<bool>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.isUpdating,
        error: (_, __) => false,
        loading: () => true,
      );
});

// 엑셀 관련 설정 Provider들
final collectDayOfWeekFromExcelProvider = Provider<bool>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.collectDayOfWeekFromExcel,
        error: (_, __) => false,
        loading: () => false,
      );
});

final excelDayOfWeekColumnIndexProvider = Provider<int>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.excelDayOfWeekColumnIndex,
        error: (_, __) => -1,
        loading: () => -1,
      );
});

final linkPrepaymentToInventoryProvider = Provider<bool>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.linkPrepaymentToInventory,
        error: (_, __) => false,
        loading: () => false,
      );
});

// UI 열 수 설정 Provider들
final inventoryColumnsProvider = Provider<int>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.inventoryColumns,
        error: (_, __) => 6, // 타블렛 기본값으로 변경 (세로 3, 가로 5 → 세로 4, 가로 6)
        loading: () => 6,
      );
});

final saleColumnsProvider = Provider<int>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.saleColumns,
        error: (_, __) => 6, // 타블렛 기본값으로 변경 (세로 3, 가로 5 → 세로 4, 가로 6)
        loading: () => 6,
      );
});

// 가로모드/세로모드별 열 수 설정 Provider들
final inventoryColumnsPortraitProvider = Provider<int>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.inventoryColumnsPortrait,
        error: (_, __) => 5, // 타블렛 세로모드 기본값으로 변경 (더 보수적)
        loading: () => 5,
      );
});

final inventoryColumnsLandscapeProvider = Provider<int>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.inventoryColumnsLandscape,
        error: (_, __) => 9, // 타블렛 가로모드 기본값으로 변경 (더 보수적)
        loading: () => 9,
      );
});

// 통합된 UI 열 수 설정: 판매 화면도 재고현황과 같은 설정 사용
final saleColumnsPortraitProvider = Provider<int>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.inventoryColumnsPortrait, // 재고현황과 통합
        error: (_, __) => 5, // 타블렛 세로모드 기본값으로 변경 (더 보수적)
        loading: () => 5,
      );
});

final saleColumnsLandscapeProvider = Provider<int>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.inventoryColumnsLandscape, // 재고현황과 통합
        error: (_, __) => 9, // 타블렛 가로모드 기본값으로 변경 (더 보수적)
        loading: () => 9,
      );
});

// 호환성을 위한 별칭
final eventDaysProvider = eventDayOfWeekProvider;

/// 기기 타입 (태블릿 여부) Provider
final isTabletProvider = Provider<bool>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.isTablet,
        error: (_, __) => false, // 기본값: 스마트폰
        loading: () => false,
      );
});

/// POS 상품 이미지 표시 여부 Provider
final showProductImagesProvider = Provider<bool>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.showProductImages,
        error: (_, __) => true, // 기본값: 이미지 표시
        loading: () => true,
      );
});

/// 스마트폰 여부 Provider (isTablet의 반대)
final isSmartphoneProvider = Provider<bool>((ref) {
  return !ref.watch(isTabletProvider);
});


