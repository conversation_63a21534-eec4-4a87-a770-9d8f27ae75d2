import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'dart:io';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../providers/nickname_provider.dart';
import '../../providers/data_sync_provider.dart';
import '../../utils/app_colors.dart';
import '../../utils/responsive_helper.dart';
import '../../utils/orientation_helper.dart';
import '../../utils/logger_utils.dart';
import '../../widgets/onboarding_components.dart';
import 'forgot_password_screen.dart';


/// 로그인 화면 - 웜톤 디자인으로 개선
///
/// 카드 기반 레이아웃과 원형 소셜 로그인 버튼을 적용한 현대적 디자인
/// 반응형 레이아웃으로 모든 기기에서 최적화된 경험 제공
class LoginScreen extends ConsumerStatefulWidget {
  final void Function(bool hasServerData) onLoginSuccess;
  final VoidCallback onRegisterRequested;
  const LoginScreen({super.key, required this.onLoginSuccess, required this.onRegisterRequested});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  // FocusNode 추가
  final FocusNode emailFocus = FocusNode();
  final FocusNode passwordFocus = FocusNode();
  bool isLoading = false;
  String? error;
  String? infoMessage;
  bool showResendButton = false;

  @override
  void initState() {
    super.initState();
    // 세로모드로 고정
    OrientationHelper.enterPortraitMode();
  }

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    emailFocus.dispose();
    passwordFocus.dispose();
    super.dispose();
  }

  Future<void> _handleLogin() async {
    setState(() { isLoading = true; error = null; infoMessage = null; showResendButton = false; });
    if (emailController.text.trim().isEmpty) {
      setState(() { error = '이메일을 입력하세요.'; isLoading = false; });
      return;
    }
    if (passwordController.text.trim().isEmpty) {
      setState(() { error = '비밀번호를 입력하세요.'; isLoading = false; });
      return;
    }
    try {
      final auth = FirebaseAuth.instance;
      final userCred = await auth.signInWithEmailAndPassword(
        email: emailController.text.trim(),
        password: passwordController.text.trim(),
      );
      if (!userCred.user!.emailVerified) {
        setState(() {
          error = '이메일 인증을 완료해야 합니다.';
          showResendButton = true;
        });
        await auth.signOut();
      } else {
        // 로그인 성공 시 이메일 인증 대기 상태 해제
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('email_verification_pending');

        // 로그인 성공 시 nicknameProvider 동기화
        await ref.read(nicknameProvider.notifier).loadNickname();

        // 서버 데이터 존재 여부 확인 및 동기화 플래그 설정
        final hasServerData = await _checkServerDataAndSetSyncFlag();

        widget.onLoginSuccess(hasServerData);
      }
    } on FirebaseAuthException catch (e) {
      setState(() { error = _firebaseErrorToKorean(e); });
    } catch (e) {
      setState(() { error = '알 수 없는 오류'; });
    } finally {
      setState(() { isLoading = false; });
    }
  }

  /// 서버 데이터 존재 여부 확인 및 동기화 플래그 설정
  Future<bool> _checkServerDataAndSetSyncFlag() async {
    try {
      final dataSyncService = ref.read(dataSyncServiceProvider);
      final hasServerData = await dataSyncService.hasServerData();

      LoggerUtils.logInfo('로그인 시 서버 데이터 확인: $hasServerData', tag: 'LoginScreen');
      LoggerUtils.logInfo('hasServerData() 메서드 결과: $hasServerData', tag: 'LoginScreen');

      // SharedPreferences에 서버 데이터 존재 여부 저장 (main.dart에서 사용)
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_server_data_on_login', hasServerData);

      if (hasServerData) {
        LoggerUtils.logInfo('서버 데이터 존재 - 동기화 확인 필요', tag: 'LoginScreen');
      } else {
        LoggerUtils.logInfo('서버 데이터 없음 - 정상 온보딩 진행', tag: 'LoginScreen');
      }
      return hasServerData;
    } catch (e) {
      LoggerUtils.logError('서버 데이터 확인 실패', tag: 'LoginScreen', error: e);
      // 확인 실패 시 안전하게 false로 설정
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_server_data_on_login', false);
      return false;
    }
  }

  /// 소셜 로그인 후 Firestore 사용자 문서 생성 (필요시)
  Future<void> _createUserDocumentIfNeeded(User user) async {
    try {
      LoggerUtils.logInfo('사용자 문서 생성 확인 시작: ${user.email}', tag: 'LoginScreen');

      final userDoc = FirebaseFirestore.instance.collection('users').doc(user.uid);
      final docSnapshot = await userDoc.get();

      if (!docSnapshot.exists) {
        LoggerUtils.logInfo('사용자 문서가 없음 - 새로 생성', tag: 'LoginScreen');

        await userDoc.set({
          'email': user.email ?? '',
          'createdAt': FieldValue.serverTimestamp(),
          'agreement': {
            'agreed': true,
            'agreedAt': FieldValue.serverTimestamp(),
          },
        }, SetOptions(merge: true));

        LoggerUtils.logInfo('사용자 문서 생성 완료: ${user.email}', tag: 'LoginScreen');
      } else {
        LoggerUtils.logInfo('사용자 문서가 이미 존재함: ${user.email}', tag: 'LoginScreen');
      }
    } catch (e) {
      LoggerUtils.logError('사용자 문서 생성 실패: ${user.email}', tag: 'LoginScreen', error: e);
      // 문서 생성 실패는 로그인 자체를 실패시키지 않음
    }
  }

  Future<void> _handleResendEmail() async {
    setState(() { isLoading = true; infoMessage = null; });
    try {
      final auth = FirebaseAuth.instance;
      final user = await auth.signInWithEmailAndPassword(
        email: emailController.text.trim(),
        password: passwordController.text.trim(),
      );
      if (user.user != null && !user.user!.emailVerified) {
        await user.user!.sendEmailVerification();
        setState(() {
          infoMessage = '인증 메일이 재발송되었습니다. 메일함을 확인해 주세요.';
          showResendButton = false;
        });
        await auth.signOut();
      } else {
        setState(() {
          infoMessage = '이미 인증이 완료된 계정입니다. 다시 로그인해 주세요.';
          showResendButton = false;
        });
      }
    } catch (e) {
      setState(() { infoMessage = '인증 메일 재발송 중 오류가 발생했습니다.'; });
    } finally {
      setState(() { isLoading = false; });
    }
  }

  Future<void> _handleForgotPassword() async {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ForgotPasswordScreen(),
      ),
    );
  }

  String _firebaseErrorToKorean(FirebaseAuthException e) {
    switch (e.code) {
      case 'invalid-email':
        return '올바른 이메일 형식이 아닙니다.';
      case 'user-not-found':
        return '해당 이메일로 가입된 계정이 없습니다.';
      case 'wrong-password':
        return '비밀번호가 올바르지 않습니다.';
      case 'too-many-requests':
        return '잠시 후 다시 시도해 주세요.';
      case 'network-request-failed':
        return '네트워크 오류가 발생했습니다.';
      case 'user-disabled':
        return '이 계정은 비활성화되어 있습니다.';
      default:
        return '입력 정보를 다시 확인해 주세요.';
    }
  }

  Future<void> _handleGoogleSignIn() async {
    setState(() { isLoading = true; error = null; });
    try {
      final GoogleSignIn signIn = GoogleSignIn.instance;
      await signIn.initialize(
        serverClientId: '************-0kgl0snkmk62ecrfkmoka6jq3dscj4he.apps.googleusercontent.com',
      );
      final GoogleSignInAccount? googleUser = await signIn.authenticate();
      if (googleUser == null) {
        setState(() { error = '구글 로그인이 취소되었습니다.'; });
        return;
      }
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        idToken: googleAuth.idToken,
      );
      final userCredential = await FirebaseAuth.instance.signInWithCredential(credential);

      // Firebase 인증 상태 동기화 대기
      await Future.delayed(const Duration(milliseconds: 500));

      final user = userCredential.user;
      if (user == null) {
        throw Exception('사용자 정보를 가져올 수 없습니다.');
      }

      LoggerUtils.logInfo('구글 로그인 성공: ${user.email}', tag: 'LoginScreen');

      // Firestore 사용자 문서 생성 (필요시)
      await _createUserDocumentIfNeeded(user);

      // 구글 로그인 성공 시 nicknameProvider 동기화
      await ref.read(nicknameProvider.notifier).loadNickname();

      // 서버 데이터 존재 여부 확인 및 동기화 플래그 설정
      final hasServerData = await _checkServerDataAndSetSyncFlag();

      LoggerUtils.logInfo('구글 로그인 초기화 완료: ${user.email}', tag: 'LoginScreen');
      widget.onLoginSuccess(hasServerData);
    } catch (e) {
      LoggerUtils.logError('구글 로그인 실패', tag: 'LoginScreen', error: e);
      setState(() { error = '구글 로그인 실패: $e'; });
    } finally {
      setState(() { isLoading = false; });
    }
  }

  Future<void> _handleAppleSignIn() async {
    setState(() { isLoading = true; error = null; });
    try {
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [AppleIDAuthorizationScopes.email, AppleIDAuthorizationScopes.fullName],
      );
      final oauthCredential = OAuthProvider('apple.com').credential(
        idToken: credential.identityToken,
        accessToken: credential.authorizationCode,
      );
      final userCredential = await FirebaseAuth.instance.signInWithCredential(oauthCredential);

      // Firebase 인증 상태 동기화 대기
      await Future.delayed(const Duration(milliseconds: 500));

      final user = userCredential.user;
      if (user == null) {
        throw Exception('사용자 정보를 가져올 수 없습니다.');
      }

      LoggerUtils.logInfo('애플 로그인 성공: ${user.email}', tag: 'LoginScreen');

      // Firestore 사용자 문서 생성 (필요시)
      await _createUserDocumentIfNeeded(user);

      // 애플 로그인 성공 시 nicknameProvider 동기화
      await ref.read(nicknameProvider.notifier).loadNickname();

      // 서버 데이터 존재 여부 확인 및 동기화 플래그 설정
      final hasServerData = await _checkServerDataAndSetSyncFlag();

      LoggerUtils.logInfo('애플 로그인 초기화 완료: ${user.email}', tag: 'LoginScreen');
      widget.onLoginSuccess(hasServerData);
    } catch (e) {
      LoggerUtils.logError('애플 로그인 실패', tag: 'LoginScreen', error: e);
      setState(() { error = '애플 로그인 실패: $e'; });
    } finally {
      setState(() { isLoading = false; });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: OnboardingComponents.buildBackground(
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              child: OnboardingComponents.buildCard(
                context: context,
                child: _buildLoginForm(context),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 로그인 폼 구성
  Widget _buildLoginForm(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 헤더
        _buildHeader(context),

        OnboardingComponents.buildSectionSpacing(context),

        // 이메일 입력
        OnboardingComponents.buildTextField(
          context: context,
          controller: emailController,
          focusNode: emailFocus,
          label: '이메일',
          hint: '<EMAIL>',
          prefixIcon: Icons.email_outlined,
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          onSubmitted: (_) => FocusScope.of(context).requestFocus(passwordFocus),
        ),

        OnboardingComponents.buildSmallSpacing(context),

        // 비밀번호 입력
        OnboardingComponents.buildTextField(
          context: context,
          controller: passwordController,
          focusNode: passwordFocus,
          label: '비밀번호',
          prefixIcon: Icons.lock_outline,
          obscureText: true,
          textInputAction: TextInputAction.done,
          onSubmitted: (_) => _handleLogin(),
        ),

        OnboardingComponents.buildSmallSpacing(context),

        // 에러 및 정보 메시지
        _buildMessages(context),

        OnboardingComponents.buildSmallSpacing(context),

        // 로그인 버튼
        OnboardingComponents.buildPrimaryButton(
          context: context,
          text: '로그인',
          onPressed: _handleLogin,
          isLoading: isLoading,
          icon: Icons.login,
        ),

        // 간결한 회원가입/비밀번호 찾기 링크
        _buildCompactAuthLinks(context),

        OnboardingComponents.buildSmallSpacing(context),

        // 구분선
        _buildDivider(context),

        OnboardingComponents.buildSmallSpacing(context),

        // 소셜 로그인
        _buildSocialLogin(context),

        OnboardingComponents.buildSmallSpacing(context),
      ],
    );
  }

  /// 간결한 회원가입/비밀번호 찾기 링크
  Widget _buildCompactAuthLinks(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        TextButton(
          onPressed: widget.onRegisterRequested,
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: Text(
            '회원가입',
            style: TextStyle(
              fontSize: ResponsiveHelper.getBodyFontSize(context),
              color: AppColors.onboardingTextSecondary.withValues(alpha: 0.7),
            ),
          ),
        ),
        Text(
          '|',
          style: TextStyle(
            fontSize: ResponsiveHelper.getBodyFontSize(context),
            color: AppColors.onboardingTextSecondary.withValues(alpha: 0.5),
          ),
        ),
        TextButton(
          onPressed: _handleForgotPassword,
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: Text(
            '비밀번호 찾기',
            style: TextStyle(
              fontSize: ResponsiveHelper.getBodyFontSize(context),
              color: AppColors.onboardingTextSecondary.withValues(alpha: 0.7),
            ),
          ),
        ),
      ],
    );
  }

  /// 헤더 섹션
  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        // 로고 아이콘
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            gradient: AppColors.primaryGradient,
            borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
            boxShadow: [
              BoxShadow(
                color: AppColors.onboardingPrimary.withValues(alpha: 0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Icon(
            Icons.login_rounded,
            size: 30,
            color: AppColors.onboardingTextOnPrimary,
          ),
        ),

        OnboardingComponents.buildSmallSpacing(context),

        // 제목
        OnboardingComponents.buildTitle(
          context: context,
          text: '로그인',
        ),

        const SizedBox(height: 8),

        // 부제목
        OnboardingComponents.buildBody(
          context: context,
          text: '바라 부스 매니저에 오신 것을 환영합니다',
        ),
      ],
    );
  }

  /// 에러 및 정보 메시지
  Widget _buildMessages(BuildContext context) {
    if (error == null && infoMessage == null && !showResendButton) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        if (error != null) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.errorLight.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.error_outline, color: AppColors.error, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    error!,
                    style: TextStyle(
                      color: AppColors.error,
                      fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
        ],

        if (infoMessage != null) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.infoLight.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.info.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.info, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    infoMessage!,
                    style: TextStyle(
                      color: AppColors.info,
                      fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
        ],

        if (showResendButton) ...[
          OnboardingComponents.buildSecondaryButton(
            context: context,
            text: '인증 메일 재발송',
            onPressed: isLoading ? null : _handleResendEmail,
            icon: Icons.email_outlined,
          ),
          const SizedBox(height: 8),
        ],
      ],
    );
  }

  /// 구분선
  Widget _buildDivider(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 1,
            color: AppColors.secondary,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            '또는',
            style: TextStyle(
              color: AppColors.onboardingTextSecondary,
              fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
            ),
          ),
        ),
        Expanded(
          child: Container(
            height: 1,
            color: AppColors.secondary,
          ),
        ),
      ],
    );
  }

  /// 소셜 로그인 섹션
  Widget _buildSocialLogin(BuildContext context) {
    final socialButtons = <Map<String, dynamic>>[];

    if (Platform.isAndroid) {
      socialButtons.add({
        'asset': 'assets/icons/Google_logo.png',
        'onPressed': _handleGoogleSignIn,
        'label': 'Google로 로그인',
        'backgroundColor': AppColors.surface,
        'textColor': AppColors.onboardingTextPrimary,
      });
    }

    if (Platform.isIOS) {
      socialButtons.add({
        'asset': 'assets/icons/Apple_logo.png',
        'onPressed': _handleAppleSignIn,
        'label': 'Apple로 로그인',
        'backgroundColor': AppColors.onboardingDarkBrown,
        'textColor': AppColors.onboardingTextOnDark,
      });
    }

    if (socialButtons.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        OnboardingComponents.buildBody(
          context: context,
          text: '소셜 계정으로 간편 로그인',
        ),

        const SizedBox(height: 16),

        OnboardingComponents.buildSocialButtonColumn(
          context: context,
          socialButtons: socialButtons,
        ),
      ],
    );
  }

}
