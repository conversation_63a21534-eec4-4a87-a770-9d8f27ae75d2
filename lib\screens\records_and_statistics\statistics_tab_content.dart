import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:parabara/widgets/adaptive_donut_chart.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../providers/sales_log_provider.dart';
import '../../providers/prepayment_provider.dart';
import '../../providers/product_provider.dart';
import '../../models/transaction_type.dart';
import '../../providers/category_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../repositories/set_discount_transaction_repository.dart';
import '../../utils/app_colors.dart';
import '../../utils/dimens.dart';
import '../../utils/currency_utils.dart';
import '../../utils/logger_utils.dart';
import '../../models/sales_log.dart';
import '../../models/product.dart';
import '../../models/prepayment.dart';
import '../../models/event_workspace.dart';
import '../../utils/dialog_theme.dart' as custom_dialog;
/// 차트 데이터 모델
class ChartData {
  final String category;
  final double value;
  final Color color;

  ChartData(this.category, this.value, this.color);
}

// 실험적 커스텀 라벨 차트 사용 여부 (안정화 전 false)
// 커스텀 라벨 강제 배치 도넛 차트 활성화
const bool kUseAdaptiveDonutChart = true;

/// 실용적인 통계 탭 컨텐츠
///
/// 실제 판매자가 알아야 하는 핵심 비즈니스 데이터에 집중한 통계 화면입니다.
/// - 매출, 할인, 선입금, 거래 유형별 상세 분석
/// - 세트 할인 제공 금액 및 적용 현황
/// - 판매자별 성과 (등수 없이)
/// - 상품별 판매 현황
/// - 일괄 판매 분석
class StatisticsTabContent extends ConsumerStatefulWidget {
  final String selectedSeller;
  final DateTimeRange? selectedDateRange;
  final TransactionType? selectedTransactionType;
  final GlobalKey? chartKey; // 차트 캡처용 GlobalKey 추가

  const StatisticsTabContent({
    super.key,
    required this.selectedSeller,
    this.selectedDateRange,
    this.selectedTransactionType,
    this.chartKey,
  });

  @override
  ConsumerState<StatisticsTabContent> createState() => _StatisticsTabContentState();
}

class _StatisticsTabContentState extends ConsumerState<StatisticsTabContent> {
  int _setDiscountAmount = 0;
  int _setDiscountCount = 0;

  @override
  void initState() {
    super.initState();
    _loadSetDiscountStats();
    _loadTimeBasedSettings();
  }

  Future<void> _loadSetDiscountStats() async {
    try {
      final currentWorkspace = ref.read(unifiedWorkspaceProvider).currentWorkspace;
      if (currentWorkspace != null) {
        final repository = SetDiscountTransactionRepository();
        final stats = await repository.getStatistics(currentWorkspace.id);
        if (mounted) {
          setState(() {
            _setDiscountAmount = stats['totalDiscountAmount'] ?? 0;
            _setDiscountCount = stats['totalAppliedCount'] ?? 0;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _setDiscountAmount = 0;
          _setDiscountCount = 0;
        });
      }
    }
  }

  /// 시간대별 매출 설정 불러오기
  Future<void> _loadTimeBasedSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentWorkspace = ref.read(unifiedWorkspaceProvider).currentWorkspace;
      if (currentWorkspace == null) return;

      // 저장된 설정 불러오기
      final savedDateString = prefs.getString('timeBased_selectedDate');
      final savedStartHour = prefs.getInt('timeBased_startHour');
      final savedEndHour = prefs.getInt('timeBased_endHour');

      if (mounted) {
        setState(() {
          // 저장된 날짜가 있고 행사 기간 내라면 사용, 아니면 행사 첫날
          if (savedDateString != null) {
            final savedDate = DateTime.tryParse(savedDateString);
            if (savedDate != null &&
                !savedDate.isBefore(currentWorkspace.startDate) &&
                !savedDate.isAfter(currentWorkspace.endDate)) {
              _selectedDate = savedDate;
            } else {
              _selectedDate = currentWorkspace.startDate;
            }
          } else {
            _selectedDate = currentWorkspace.startDate;
          }

          // 저장된 시간 설정이 있으면 사용, 아니면 기본값 (10시~17시)
          _startHour = savedStartHour ?? 10;
          _endHour = savedEndHour ?? 17;
        });
      }
    } catch (e) {
      LoggerUtils.logError('시간대별 매출 설정 불러오기 실패', error: e);
      // 오류 시 기본값 설정
      final currentWorkspace = ref.read(unifiedWorkspaceProvider).currentWorkspace;
      if (currentWorkspace != null && mounted) {
        setState(() {
          _selectedDate = currentWorkspace.startDate;
          _startHour = 10;
          _endHour = 17;
        });
      }
    }
  }

  /// 시간대별 매출 설정 저장하기
  Future<void> _saveTimeBasedSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setString('timeBased_selectedDate', _selectedDate!.toIso8601String());
      await prefs.setInt('timeBased_startHour', _startHour);
      await prefs.setInt('timeBased_endHour', _endHour);
    } catch (e) {
      LoggerUtils.logError('시간대별 매출 설정 저장 실패', error: e);
    }
  }

  @override
  Widget build(BuildContext context) {
    final salesLogState = ref.watch(salesLogNotifierProvider);
    final prepaymentState = ref.watch(prepaymentNotifierProvider);
    // 깜빡임 방지: 제품/카테고리는 선택자/현재값 Provider로 최소 리빌드
    // 안전장치: 로딩 상태가 아닌 경우에만 데이터 사용
    final productState = ref.watch(productNotifierProvider);
    final products = productState.isLoading ? <Product>[] : productState.products;
    final categories = ref.watch(currentCategoriesProvider);

    if (salesLogState.isLoading || prepaymentState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (salesLogState.hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: Dimens.space16),
            Text(
              '데이터를 불러올 수 없습니다',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.error,
                fontFamily: 'Pretendard',
              ),
            ),
          ],
        ),
      );
    }

    // productId -> categoryName 매핑 생성 (안정적, 한 번만 계산)
    Map<int, String>? productCategoryMap;
    if (products.isNotEmpty && categories.isNotEmpty) {
      productCategoryMap = <int, String>{};
      for (final product in products) {
        if (product.id != null) {
          final matched = categories.where((c) => c.id == product.categoryId);
          if (matched.isNotEmpty) {
            productCategoryMap[product.id!] = matched.first.name;
          }
        }
      }
    }

    // 필터링된 데이터 계산
    final filteredSalesLogs = _getFilteredSalesLogs(salesLogState.salesLogs);
    final filteredPrepayments = _getFilteredPrepayments(prepaymentState.prepayments);
    final statsData = _calculateStatistics(filteredSalesLogs, filteredPrepayments, productCategoryMap: productCategoryMap);

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
        await ref.read(prepaymentNotifierProvider.notifier).loadPrepayments();
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(Dimens.space16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 모던 대시보드 레이아웃
            _buildModernDashboard(
              context: context,
              stats: statsData,
              logs: filteredSalesLogs,
              prepayments: filteredPrepayments,
            ),
            const SizedBox(height: Dimens.space24),
          ],
        ),
      ),
    );
  }

  /// 필터링된 판매 기록 반환
  List<SalesLog> _getFilteredSalesLogs(List<SalesLog> allLogs) {
    return allLogs.where((log) {
      // 판매자 필터
      if (widget.selectedSeller != '전체 판매자' &&
          (log.sellerName ?? '알 수 없음') != widget.selectedSeller) {
        return false;
      }

      // 날짜 범위 필터
      if (widget.selectedDateRange != null) {
        final logDate = DateTime.fromMillisecondsSinceEpoch(log.saleTimestamp);
        final startDate = widget.selectedDateRange!.start;
        final endDate = widget.selectedDateRange!.end.add(const Duration(days: 1));

        if (logDate.isBefore(startDate) || logDate.isAfter(endDate)) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  /// 필터링된 선입금 기록 반환
  List<Prepayment> _getFilteredPrepayments(List<Prepayment> allPrepayments) {
    return allPrepayments.where((prepayment) {
      // 날짜 범위 필터
      if (widget.selectedDateRange != null) {
        final prepaymentDate = prepayment.registrationDate;
        final startDate = widget.selectedDateRange!.start;
        final endDate = widget.selectedDateRange!.end.add(const Duration(days: 1));

        if (prepaymentDate.isBefore(startDate) || prepaymentDate.isAfter(endDate)) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  /// 통계 데이터 계산
  Map<String, dynamic> _calculateStatistics(List<SalesLog> logs, List<Prepayment> prepayments, {Map<int, String>? productCategoryMap}) {
    if (logs.isEmpty && prepayments.isEmpty) {
      return _getEmptyStats();
    }

    // 기본 매출 통계
    final totalRevenue = logs.fold<int>(0, (sum, log) => sum + log.totalAmount);

    // 실제 거래 건수 계산 (batchSaleId로 그룹핑)
    final uniqueTransactions = <String>{};
    for (final log in logs) {
      if (log.batchSaleId != null) {
        uniqueTransactions.add(log.batchSaleId!);
      } else {
        // batchSaleId가 없으면 개별 거래로 간주
        uniqueTransactions.add('single_${log.id}_${log.saleTimestamp}');
      }
    }
    final totalTransactions = uniqueTransactions.length;

    final averageTransaction = totalTransactions > 0 ? totalRevenue ~/ totalTransactions : 0;
    final totalQuantity = logs.fold<int>(0, (sum, log) => sum + log.soldQuantity);

    // 세트 할인 분석 (새로운 방식: SetDiscountTransaction 테이블 사용)
    final totalSetDiscountAmount = _setDiscountAmount;
    final setDiscountCount = _setDiscountCount;

    final originalTotalBeforeDiscount = logs.fold<int>(0, (sum, log) =>
        sum + (log.soldPrice * log.soldQuantity));

    // 거래 유형별 분석 (건수와 금액)
    final transactionTypeStats = <TransactionType, Map<String, int>>{};
    for (final type in TransactionType.values) {
      final typeLogs = logs.where((log) => log.transactionType == type).toList();
      transactionTypeStats[type] = {
        'count': typeLogs.length,
        'amount': typeLogs.fold<int>(0, (sum, log) => sum + log.totalAmount),
      };
    }

    // 상품별 분석 (수량과 매출) - 카테고리명 포함
    final productStats = <String, Map<String, int>>{};
    for (final log in logs) {
      String productDisplayName = log.productName;

      // 카테고리명 포함 표시
      if (log.productId != null && productCategoryMap != null) {
        final categoryName = productCategoryMap[log.productId];
        if (categoryName != null) {
          productDisplayName = '$categoryName-${log.productName}';
        }
      }

      if (!productStats.containsKey(productDisplayName)) {
        productStats[productDisplayName] = {'quantity': 0, 'revenue': 0};
      }
      productStats[productDisplayName]!['quantity'] =
          (productStats[productDisplayName]!['quantity'] ?? 0) + log.soldQuantity;
      productStats[productDisplayName]!['revenue'] =
          (productStats[productDisplayName]!['revenue'] ?? 0) + log.totalAmount;
    }

    // 카테고리별 분석 (매출과 수량)
    final categoryStats = <String, Map<String, int>>{};
    for (final log in logs) {
      String categoryName = '기타'; // 기본값

      // productId가 있고 productCategoryMap이 있으면 카테고리명 찾기
      if (log.productId != null && productCategoryMap != null) {
        categoryName = productCategoryMap[log.productId] ?? '기타';
      }

      if (!categoryStats.containsKey(categoryName)) {
        categoryStats[categoryName] = {'quantity': 0, 'revenue': 0};
      }
      categoryStats[categoryName]!['quantity'] =
          (categoryStats[categoryName]!['quantity'] ?? 0) + log.soldQuantity;
      categoryStats[categoryName]!['revenue'] =
          (categoryStats[categoryName]!['revenue'] ?? 0) + log.totalAmount;
    }

    // 판매자별 성과 (실제 거래 건수 기준)
    final sellerStats = <String, Map<String, int>>{};
    final sellerTransactions = <String, Set<String>>{};

    for (final log in logs) {
      final seller = log.sellerName ?? '알 수 없음';
      if (!sellerStats.containsKey(seller)) {
        sellerStats[seller] = {'count': 0, 'revenue': 0};
        sellerTransactions[seller] = <String>{};
      }

      // 거래 ID 생성 (batchSaleId 또는 개별 거래)
      final transactionId = log.batchSaleId ?? 'single_${log.id}_${log.saleTimestamp}';
      sellerTransactions[seller]!.add(transactionId);

      sellerStats[seller]!['revenue'] = (sellerStats[seller]!['revenue'] ?? 0) + log.totalAmount;
    }

    // 실제 거래 건수 설정
    for (final seller in sellerStats.keys) {
      sellerStats[seller]!['count'] = sellerTransactions[seller]!.length;
    }

    // 선입금 분석
    final totalPrepaymentAmount = prepayments.fold<int>(0, (sum, p) => sum + p.amount);
    final receivedPrepayments = prepayments.where((p) => p.isReceived).toList();
    final receivedPrepaymentAmount = receivedPrepayments.fold<int>(0, (sum, p) => sum + p.amount);
    final pendingPrepaymentAmount = totalPrepaymentAmount - receivedPrepaymentAmount;

    // 서비스 제공 분석 (판매가격이 0인 경우를 서비스로 간주)
    final serviceLogs = logs.where((log) => log.soldPrice == 0).toList();
    final serviceCount = serviceLogs.length;
    final serviceQuantity = serviceLogs.fold<int>(0, (sum, log) => sum + log.soldQuantity);
    final uniqueServiceProducts = serviceLogs.map((log) => log.productName).toSet().length;

    // 세트별 할인 통계 생성 (엑셀 내보내기와 동일한 방식)
    final setDiscountLogs = logs.where((log) => log.setDiscountNames != null && log.setDiscountNames!.isNotEmpty).toList();
    final Map<String, Map<String, int>> setDiscountStats = {};

    for (final log in setDiscountLogs) {
      final setNames = log.setDiscountNames!.split(',').map((name) => name.trim()).toList();
      for (final setName in setNames) {
        if (!setDiscountStats.containsKey(setName)) {
          setDiscountStats[setName] = {'count': 0, 'totalDiscount': 0};
        }
        setDiscountStats[setName]!['count'] = (setDiscountStats[setName]!['count'] ?? 0) + 1;
        // 여러 세트가 적용된 경우 할인액을 균등 분배
        final discountPerSet = setNames.length > 1 ? (log.setDiscountAmount / setNames.length).round() : log.setDiscountAmount;
        setDiscountStats[setName]!['totalDiscount'] = (setDiscountStats[setName]!['totalDiscount'] ?? 0) + discountPerSet;
      }
    }

    return {
      // 기본 지표
      'totalRevenue': totalRevenue,
      'totalTransactions': totalTransactions,
      'averageTransaction': averageTransaction,
      'totalQuantity': totalQuantity,

      // 할인 분석
      'totalSetDiscountAmount': totalSetDiscountAmount,
      'setDiscountCount': setDiscountCount,
      'originalTotalBeforeDiscount': originalTotalBeforeDiscount,
      'setDiscountStats': setDiscountStats, // 세트별 할인 통계 추가

      // 거래 유형별
      'transactionTypeStats': transactionTypeStats,

      // 상품별
      'productStats': productStats,

      // 카테고리별
      'categoryStats': categoryStats,

      // 판매자별
      'sellerStats': sellerStats,

      // 선입금
      'totalPrepaymentAmount': totalPrepaymentAmount,
      'receivedPrepaymentAmount': receivedPrepaymentAmount,
      'pendingPrepaymentAmount': pendingPrepaymentAmount,
      'totalPrepaymentCount': prepayments.length,
      'receivedPrepaymentCount': receivedPrepayments.length,
      'pendingPrepaymentCount': prepayments.length - receivedPrepayments.length,

      // 서비스 제공
      'serviceCount': serviceCount,
      'serviceQuantity': serviceQuantity,
      'uniqueServiceProducts': uniqueServiceProducts,

      // 시간대별 분석을 위한 원본 로그
      'salesLogs': logs,
    };
  }

  /// 빈 통계 데이터 반환
  Map<String, dynamic> _getEmptyStats() {
    return {
      'totalRevenue': 0,
      'totalTransactions': 0,
      'averageTransaction': 0,
      'totalQuantity': 0,
      'totalSetDiscountAmount': 0,
      'setDiscountCount': 0,
      'originalTotalBeforeDiscount': 0,
      'transactionTypeStats': <TransactionType, Map<String, int>>{},
      'productStats': <String, Map<String, int>>{},
      'categoryStats': <String, Map<String, int>>{},
      'sellerStats': <String, Map<String, int>>{},
      'totalPrepaymentAmount': 0,
      'receivedPrepaymentAmount': 0,
      'pendingPrepaymentAmount': 0,
      'totalPrepaymentCount': 0,
      'receivedPrepaymentCount': 0,
      'pendingPrepaymentCount': 0,
      'serviceCount': 0,
      'serviceQuantity': 0,
      'uniqueServiceProducts': 0,
    };
  }



  /// 세트 분석 섹션
  Widget _buildSetAnalysisSection(Map<String, dynamic> stats) {
    final discountAmount = stats['totalSetDiscountAmount'] ?? 0;
    final discountCount = stats['setDiscountCount'] ?? 0;
    final originalTotal = stats['originalTotalBeforeDiscount'] ?? 0;
    final discountRate = originalTotal > 0 ? (discountAmount / originalTotal * 100) : 0.0;

    return SingleChildScrollView(
      child: Column(
        children: [
          // 전체 요약
          Container(
            padding: const EdgeInsets.all(Dimens.space16),
            decoration: BoxDecoration(
              color: AppColors.neutral10,
              borderRadius: BorderRadius.circular(Dimens.radiusM),
            ),
            child: Column(
              children: [
                _buildStatRow(
                  '세트 할인 제공 금액',
                  CurrencyUtils.formatCurrency(discountAmount),
                  Icons.savings_outlined,
                  AppColors.success,
                ),
                const SizedBox(height: Dimens.space12),
                _buildStatRow(
                  '세트 할인 적용 횟수',
                  '${discountCount}회',
                  Icons.local_offer_outlined,
                  AppColors.info,
                ),
                const SizedBox(height: Dimens.space12),
                _buildStatRow(
                  '할인율',
                  '${discountRate.toStringAsFixed(1)}%',
                  Icons.percent_outlined,
                  AppColors.accent,
                ),
                const SizedBox(height: Dimens.space12),
                _buildStatRow(
                  '할인 전 총액',
                  CurrencyUtils.formatCurrency(originalTotal),
                  Icons.receipt_outlined,
                  AppColors.neutral60,
                ),
              ],
            ),
          ),
          const SizedBox(height: Dimens.space20),

          // 세트별 상세 정보
          _buildSetDetailsSection(stats),
        ],
      ),
    );
  }

  /// 모던 대시보드 레이아웃
  Widget _buildModernDashboard({
    required BuildContext context,
    required Map<String, dynamic> stats,
    required List<SalesLog> logs,
    required List<Prepayment> prepayments,
  }) {
    final width = MediaQuery.of(context).size.width;
    final isTablet = width >= 600;
    final isWide = width >= 1000;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Hero Section - 총 매출 강조
        _buildHeroSection(stats, isTablet, isWide),
        const SizedBox(height: Dimens.space20),

        // Analytics Section - 차트와 시각화
        _buildAnalyticsSection(stats, isTablet, isWide),
        const SizedBox(height: Dimens.space20),

        // Secondary Metrics - 보조 지표들
        _buildSecondaryMetrics(stats, isTablet, isWide),
      ],
    );
  }

  /// Hero Section - 핵심 지표 통합 카드
  Widget _buildHeroSection(Map<String, dynamic> stats, bool isTablet, bool isWide) {
    return _buildCoreMetricsCard(stats);
  }

  /// 핵심 지표 통합 카드
  Widget _buildCoreMetricsCard(Map<String, dynamic> stats) {
    return Container(
        padding: const EdgeInsets.all(Dimens.space24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(Dimens.radiusL),
          border: Border.all(
            color: Colors.grey.shade300,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 헤더
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(Dimens.space8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2563EB).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(Dimens.radiusS),
                  ),
                  child: const Icon(
                    Icons.dashboard_rounded,
                    color: Color(0xFF2563EB),
                    size: 20,
                  ),
                ),
                const SizedBox(width: Dimens.space12),
                Text(
                  '핵심 지표',
                  style: TextStyle(
                    fontFamily: 'Pretendard',
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
                const Spacer(),
                // > 표시 제거됨 (연결된 다이얼로그가 없으므로)
              ],
            ),
            const SizedBox(height: Dimens.space20),
            // 지표들
            _buildMetricsGrid(stats),
          ],
        ),
      );
  }

  /// 지표 그리드 빌더 (2x2 형태)
  Widget _buildMetricsGrid(Map<String, dynamic> stats) {
    // 지표 데이터
    final metrics = [
      {
        'title': '총 매출',
        'value': CurrencyUtils.formatCurrency(stats['totalRevenue']),
        'icon': Icons.account_balance_wallet_rounded,
        'color': const Color(0xFF2563EB), // 파란색
      },
      {
        'title': '총 거래수',
        'value': '${stats['totalTransactions']}건',
        'icon': Icons.receipt_long_rounded,
        'color': const Color(0xFF059669), // 초록색
      },
      {
        'title': '평균 거래액',
        'value': CurrencyUtils.formatCurrency(stats['averageTransaction']),
        'icon': Icons.trending_up_rounded,
        'color': const Color(0xFF7C3AED), // 보라색
      },
      {
        'title': '총 판매량',
        'value': '${stats['totalQuantity']}개',
        'icon': Icons.inventory_rounded,
        'color': const Color(0xFF0891B2), // 청록색
      },
    ];

    return Column(
      children: [
        // 첫 번째 행: 총 매출, 총 거래수
        IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(child: _buildSimpleMetricRow(metrics[0])),
              const SizedBox(width: Dimens.space16),
              Expanded(child: _buildSimpleMetricRow(metrics[1])),
            ],
          ),
        ),
        const SizedBox(height: Dimens.space16),
        // 두 번째 행: 평균 거래액, 총 판매량
        IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(child: _buildSimpleMetricRow(metrics[2])),
              const SizedBox(width: Dimens.space16),
              Expanded(child: _buildSimpleMetricRow(metrics[3])),
            ],
          ),
        ),
      ],
    );
  }

  /// 단순한 지표 행
  Widget _buildSimpleMetricRow(Map<String, dynamic> metric) {
    return Container(
      height: 70, // 고정 높이로 일관성 유지
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // 첫 번째 줄: 아이콘과 제목
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(Dimens.space8),
                decoration: BoxDecoration(
                  color: (metric['color'] as Color).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(Dimens.radiusS),
                ),
                child: Icon(
                  metric['icon'] as IconData,
                  color: metric['color'] as Color,
                  size: 20,
                ),
              ),
              const SizedBox(width: Dimens.space12),
              Expanded(
                child: Text(
                  metric['title'] as String,
                  style: TextStyle(
                    fontFamily: 'Pretendard',
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade600,
                    height: 1.0, // line height 줄여서 간격 최소화
                  ),
                ),
              ),
            ],
          ),
          // 두 번째 줄: 수치 (아이콘과 제목의 시작점에 맞춰 정렬)
          Padding(
            padding: const EdgeInsets.only(left: 48.0), // 아이콘 컨테이너(36px) + 간격(12px)
            child: Text(
              metric['value'] as String,
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
                height: 1.0, // line height 줄여서 간격 최소화
              ),
            ),
          ),
        ],
      ),
    );
  }



  /// Analytics Section - 차트와 분석
  Widget _buildAnalyticsSection(Map<String, dynamic> stats, bool isTablet, bool isWide) {
    if (isWide) {
      return Column(
        children: [
          // 상단: 거래유형 차트와 상품별 차트를 가로로 배치
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 2,
                child: _buildTransactionTypeChart(stats),
              ),
              const SizedBox(width: Dimens.space16),
              Expanded(
                flex: 3,
                child: _buildProductChart(stats),
              ),
            ],
          ),
          const SizedBox(height: Dimens.space16),
          // 하단: 시간대별 차트를 전체 너비로 배치
          _buildTimeBasedChart(stats),
        ],
      );
    } else {
      return Column(
        children: [
          _buildTransactionTypeChart(stats),
          const SizedBox(height: Dimens.space16),
          _buildTimeBasedChart(stats),
          const SizedBox(height: Dimens.space16),
          _buildProductChart(stats),
        ],
      );
    }
  }
  /// 카테고리별 도넛 차트
  Widget _buildTransactionTypeChart(Map<String, dynamic> stats) {
    final categoryStats = stats['categoryStats'] as Map<String, Map<String, int>>;

    // 가독성 중심 색상 팔레트
    final List<Color> colors = [
      const Color(0xFF2563EB), // 파란색
      const Color(0xFF059669), // 초록색
      const Color(0xFF7C3AED), // 보라색
      const Color(0xFFDC2626), // 빨간색
      const Color(0xFF0891B2), // 청록색
      const Color(0xFFEA580C), // 주황색
    ];

    // 매출 기준으로 정렬하고 상위 6개만 선택
    final sortedCategories = categoryStats.entries.toList()
      ..sort((a, b) => (b.value['revenue'] ?? 0).compareTo(a.value['revenue'] ?? 0));

    final topCategories = sortedCategories.take(6).toList();
    final totalRevenue = categoryStats.values.fold<int>(0, (sum, stats) => sum + (stats['revenue'] ?? 0));

    // 차트 데이터 준비
    final List<ChartData> chartData = [];

    int colorIndex = 0;
    for (final entry in topCategories) {
      final amount = entry.value['revenue'] ?? 0;
      if (amount > 0) {
        chartData.add(ChartData(
          entry.key,
          amount.toDouble(),
          colors[colorIndex % colors.length],
        ));
        colorIndex++;
      }
    }

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: categoryStats.isNotEmpty ? () => _showCategoryDetailsDialog(stats) : null,
        borderRadius: BorderRadius.circular(Dimens.radiusL),
        child: Container(
          padding: const EdgeInsets.all(Dimens.space20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(Dimens.radiusL),
            border: Border.all(
              color: Colors.grey.shade300,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(Dimens.space8),
                decoration: BoxDecoration(
                  color: const Color(0xFF2563EB).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(Dimens.radiusS),
                ),
                child: const Icon(
                  Icons.pie_chart_rounded,
                  color: Color(0xFF2563EB),
                  size: 20,
                ),
              ),
              const SizedBox(width: Dimens.space12),
              Expanded(
                child: Text(
                  '카테고리별 판매',
                  style: TextStyle(
                    fontFamily: 'Pretendard',
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
              ),
              if (categoryStats.isNotEmpty)
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: Colors.grey.shade400,
                  size: 14,
                ),
            ],
          ),
          const SizedBox(height: Dimens.space20),
          if (chartData.isNotEmpty && !kUseAdaptiveDonutChart)
            RepaintBoundary(
              key: widget.chartKey,
              child: SizedBox(
                height: 250, // 200 → 250으로 크기 증가
                child: SfCircularChart(
                legend: Legend(
                  isVisible: true,
                  position: LegendPosition.right,
                  textStyle: const TextStyle(
                    fontFamily: 'Pretendard',
                    fontSize: 11,
                  ),
                  legendItemBuilder: (String name, dynamic series, dynamic point, int index) {
                    final dataItem = chartData[index];
                    final percentage = ((dataItem.value / totalRevenue) * 100);
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              color: dataItem.color,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 6),
                          Text(
                            '${dataItem.category} (${percentage.toStringAsFixed(1)}%)',
                            style: const TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: 11,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
                series: <CircularSeries>[
                  DoughnutSeries<ChartData, String>(
                    dataSource: chartData,
                    xValueMapper: (ChartData data, _) => data.category,
                    yValueMapper: (ChartData data, _) => data.value,
                    pointColorMapper: (ChartData data, _) => data.color,
                    innerRadius: '60%',
                    dataLabelSettings: DataLabelSettings(
                      isVisible: true,
                      labelPosition: ChartDataLabelPosition.outside, // 외부로 변경
                      textStyle: const TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                      labelIntersectAction: LabelIntersectAction.shift,
                      builder: (dynamic data, dynamic point, dynamic series, int pointIndex, int seriesIndex) {
                        final chartData = data as ChartData;
                        return Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 2,
                              ),
                            ],
                          ),
                          child: Text(
                            chartData.category,
                            style: const TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
              ),
            )
          else if (chartData.isNotEmpty && kUseAdaptiveDonutChart)
            RepaintBoundary(
              key: widget.chartKey,
              child: Column(
                children: [
                  Center(
                    child: LayoutBuilder(builder: (context, cts){
                      final fullWidth = cts.maxWidth; // 카드의 가용 폭 전부 사용
                      return SizedBox(
                        width: fullWidth,
                        child: AdaptiveDonutChart(
                          segments: [
                            for (final c in chartData)
                              DonutSegment(label: c.category, value: c.value, color: c.color),
                          ],
                          size: 260,              // 높이/도넛 기본 크기 (정사각형 높이 역할)
                          donutScale: 0.68,       // 조금 더 작게 (라벨 여유 증가)
                          innerRadiusFraction: 0.58,
                          labelPadding: 18,       // 도넛 외곽과 라벨 시작 거리
                          fullWidth: fullWidth,   // 전체 폭 -> 라벨 공간으로 활용
                        ),
                      );
                    }),
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    alignment: WrapAlignment.center,
                    spacing: 16,
                    runSpacing: 8,
                    children: [
                      for (final d in chartData)
                        Builder(builder: (context) {
                          final p = (d.value / totalRevenue) * 100;
                          return Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: d.color,
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              ),
                              const SizedBox(width: 6),
                              Text(
                                '${d.category} (${p.toStringAsFixed(1)}%)',
                                style: const TextStyle(
                                  fontFamily: 'Pretendard',
                                  fontSize: 11,
                                  color: Colors.black87,
                                ),
                              ),
                            ],
                          );
                        })
                    ],
                  ),
                ],
              ),
            )
          else
            const SizedBox(
              height: 250, // 200 → 250으로 크기 증가
              child: Center(
                child: Text(
                  '데이터가 없습니다',
                  style: TextStyle(
                    fontFamily: 'Pretendard',
                    color: AppColors.neutral60,
                  ),
                ),
              ),
            ),
        ],
          ),
        ),
      ),
    );
  }

  /// 시간대별 매출 차트
  Widget _buildTimeBasedChart(Map<String, dynamic> stats) {
    // 현재 행사의 날짜 정보 가져오기
    final currentWorkspace = ref.watch(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      return Container(
        height: 200,
        padding: const EdgeInsets.all(Dimens.space20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(Dimens.radiusL),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const Center(
          child: Text(
            '행사 정보를 불러올 수 없습니다',
            style: TextStyle(
              fontFamily: 'Pretendard',
              color: AppColors.neutral60,
            ),
          ),
        ),
      );
    }

    // 시간대별 매출 데이터 생성 (날짜가 설정되지 않았으면 빈 데이터)
    final timeBasedData = _selectedDate != null ? _generateTimeBasedData(stats) : <ChartData>[];

    return Container(
      padding: const EdgeInsets.all(Dimens.space20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(Dimens.radiusL),
        border: Border.all(
          color: const Color(0xFF7C3AED).withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _showTimeBasedAnalysisDialog(stats),
        borderRadius: BorderRadius.circular(Dimens.radiusL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 헤더 (아이콘과 제목)
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(Dimens.space8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF7C3AED).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(Dimens.radiusS),
                  ),
                  child: const Icon(
                    Icons.timeline_rounded,
                    color: Color(0xFF7C3AED),
                    size: 20,
                  ),
                ),
                const SizedBox(width: Dimens.space12),
                Text(
                  '시간대별 매출',
                  style: TextStyle(
                    fontFamily: 'Pretendard',
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
                const SizedBox(width: Dimens.space8),
                GestureDetector(
                  onTap: _showTimeBasedSettings,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: Dimens.space6,
                      vertical: Dimens.space4,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF7C3AED).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(Dimens.radiusS),
                    ),
                    child: const Icon(
                      Icons.settings,
                      size: 14,
                      color: Color(0xFF7C3AED),
                    ),
                  ),
                ),
                Expanded(child: Container()),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey.shade400,
                  size: 14,
                ),
              ],
            ),
            const SizedBox(height: Dimens.space16),

            // 선택된 날짜와 시간 범위 표시
            Text(
              _selectedDate != null
                ? '${_formatDate(_selectedDate!)} (${_startHour}시~${_endHour}시)'
                : '날짜 설정 중...',
              style: const TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.onSurface,
              ),
            ),
            const SizedBox(height: Dimens.space12),

          // 미니 차트
          SizedBox(
            height: 120,
            child: timeBasedData.isNotEmpty
                ? SfCartesianChart(
                    primaryXAxis: CategoryAxis(
                      labelStyle: const TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 9,
                        color: Color.fromARGB(255, 0, 0, 0),
                      ),
                      maximumLabelWidth: 60,
                    ),
                    primaryYAxis: NumericAxis(
                      labelFormat: '{value}',
                      axisLabelFormatter: (AxisLabelRenderDetails details) {
                        return ChartAxisLabel(
                          CurrencyUtils.formatCurrencyInManUnit(details.value.toInt()),
                          details.textStyle,
                        );
                      },
                      labelStyle: const TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 9,
                        color: Color.fromARGB(255, 0, 0, 0),
                      ),
                    ),
                    plotAreaBorderWidth: 0,
                    series: <CartesianSeries>[
                      LineSeries<ChartData, String>(
                        dataSource: timeBasedData,
                        xValueMapper: (ChartData data, _) => data.category,
                        yValueMapper: (ChartData data, _) => data.value,
                        color: const Color(0xFF7C3AED),
                        width: 2,
                        markerSettings: const MarkerSettings(
                          isVisible: true,
                          height: 4,
                          width: 4,
                          color: Color(0xFF7C3AED),
                        ),
                      ),
                    ],
                  )
                : const Center(
                    child: Text(
                      '데이터가 없습니다',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        color: AppColors.neutral60,
                        fontSize: 12,
                      ),
                    ),
                  ),
          ),
        ],
      ),
    ),
    );
  }

  /// 상품별 매출 카드
  Widget _buildProductChart(Map<String, dynamic> stats) {
    final productStats = stats['productStats'] as Map<String, Map<String, int>>;

    // TOP 5 상품 데이터 준비 (차트용)
    final sortedProducts = productStats.entries.toList()
      ..sort((a, b) => (b.value['revenue'] ?? 0).compareTo(a.value['revenue'] ?? 0));

    final List<ChartData> chartData = [];
    final topProducts = sortedProducts.take(5).toList();

    // 가독성 중심 색상 (그라데이션)
    const baseColor = Color(0xFF059669); // 초록색
    // 차트에서 큰 값이 위쪽에 오도록 역순으로 처리
    for (int i = 0; i < topProducts.length; i++) {
      final entry = topProducts[topProducts.length - 1 - i]; // 역순 인덱스
      final revenue = entry.value['revenue'] ?? 0;
      chartData.add(ChartData(
        entry.key.length > 10 ? '${entry.key.substring(0, 10)}...' : entry.key,
        revenue.toDouble(),
        baseColor.withValues(alpha: 1.0 - ((topProducts.length - 1 - i) * 0.15)), // 색상도 역순에 맞게 조정
      ));
    }



    return _buildMetricCard(
      title: '상품별 매출',
      icon: Icons.bar_chart_rounded,
      color: const Color(0xFF059669),
      onTap: () => _showProductAnalysisDialog(stats),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 차트
          if (chartData.isNotEmpty)
            SizedBox(
              height: 230,
              child: SfCartesianChart(
                primaryXAxis: CategoryAxis(
                  labelStyle: const TextStyle(
                    fontFamily: 'Pretendard',
                    fontSize: 10,
                    color: Color.fromARGB(255, 0, 0, 0),
                  ),
                  maximumLabelWidth: 120,
                  labelIntersectAction: AxisLabelIntersectAction.multipleRows,
                ),
                primaryYAxis: NumericAxis(
                  labelFormat: '{value}',
                  axisLabelFormatter: (AxisLabelRenderDetails details) {
                    return ChartAxisLabel(
                      CurrencyUtils.formatCurrencyInManUnit(details.value.toInt()),
                      details.textStyle,
                    );
                  },
                  labelStyle: const TextStyle(
                    fontFamily: 'Pretendard',
                    fontSize: 10,
                    color: Color.fromARGB(255, 0, 0, 0),
                  ),
                ),
                plotAreaBorderWidth: 0,
                series: <CartesianSeries>[
                  BarSeries<ChartData, String>(
                    dataSource: chartData,
                    xValueMapper: (ChartData data, _) => data.category,
                    yValueMapper: (ChartData data, _) => data.value,
                    pointColorMapper: (ChartData data, _) => data.color,
                    borderRadius: const BorderRadius.all(Radius.circular(4)),
                    width: chartData.length == 1 ? 0.3 : 0.6,
                    spacing: 0.1,
                    dataLabelSettings: DataLabelSettings(
                      isVisible: true, // 정확한 수치 표시
                      textStyle: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey.shade700,
                      ),
                      labelAlignment: ChartDataLabelAlignment.outer,
                    ),
                  ),
                ],
              ),
            )
          else
            SizedBox(
              height: 230,
              child: Center(
                child: Text(
                  '데이터가 없습니다',
                  style: TextStyle(
                    fontFamily: 'Pretendard',
                    color: Colors.grey.shade500,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
  /// Secondary Metrics - 보조 지표들
  Widget _buildSecondaryMetrics(Map<String, dynamic> stats, bool isTablet, bool isWide) {
    final List<Widget> cards = [
      _buildDiscountCard(stats),
      _buildPrepaymentCard(stats),
      _buildServiceCard(stats),
      if (widget.selectedSeller == '전체 판매자')
        _buildSellerCard(stats),
    ];

    if (isWide) {
      return Wrap(
        spacing: Dimens.space16,
        runSpacing: Dimens.space16,
        children: cards.map((card) => SizedBox(
          width: (MediaQuery.of(context).size.width - Dimens.space32 - Dimens.space16 * 2) / 3,
          child: card,
        )).toList(),
      );
    } else if (isTablet) {
      return Wrap(
        spacing: Dimens.space16,
        runSpacing: Dimens.space16,
        children: cards.map((card) => SizedBox(
          width: (MediaQuery.of(context).size.width - Dimens.space32 - Dimens.space16) / 2,
          child: card,
        )).toList(),
      );
    } else {
      return Column(
        children: cards.map((card) => Padding(
          padding: const EdgeInsets.only(bottom: Dimens.space16),
          child: card,
        )).toList(),
      );
    }
  }

  /// 세트 분석 카드
  Widget _buildDiscountCard(Map<String, dynamic> stats) {
    final discountAmount = stats['totalSetDiscountAmount'] ?? 0;
    final discountCount = stats['setDiscountCount'] ?? 0;
    final originalTotal = stats['originalTotalBeforeDiscount'] ?? 0;
    final discountRate = originalTotal > 0 ? (discountAmount / originalTotal * 100) : 0.0;

    return _buildMetricCard(
      title: '세트 분석',
      icon: Icons.savings_rounded,
      color: AppColors.success,
      onTap: () => _showSetAnalysisDialog(stats),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            CurrencyUtils.formatCurrency(discountAmount),
            style: const TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.onSurface,
            ),
          ),
          const SizedBox(height: Dimens.space8),
          Text(
            '${discountCount}건 적용 · ${discountRate.toStringAsFixed(1)}% 할인률',
            style: TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 12,
              color: AppColors.neutral60,
            ),
          ),
          const SizedBox(height: Dimens.space12),
          LinearProgressIndicator(
            value: discountRate / 100,
            backgroundColor: AppColors.success.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.success),
          ),
        ],
      ),
    );
  }

  /// 선입금 현황 카드
  Widget _buildPrepaymentCard(Map<String, dynamic> stats) {
    final receivedAmount = stats['receivedPrepaymentAmount'] ?? 0;
    final pendingAmount = stats['pendingPrepaymentAmount'] ?? 0;
    final totalAmount = receivedAmount + pendingAmount;
    final receivedRate = totalAmount > 0 ? (receivedAmount / totalAmount * 100) : 0.0;

    return _buildMetricCard(
      title: '선입금 현황',
      icon: Icons.account_balance_rounded,
      color: AppColors.info,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            CurrencyUtils.formatCurrency(receivedAmount),
            style: const TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.onSurface,
            ),
          ),
          const SizedBox(height: Dimens.space4),
          Text(
            '수령 완료',
            style: TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 12,
              color: AppColors.neutral60,
            ),
          ),
          const SizedBox(height: Dimens.space12),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '수령률',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 10,
                        color: AppColors.neutral60,
                      ),
                    ),
                    Text(
                      '${receivedRate.toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.info,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '미수령',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 10,
                        color: AppColors.neutral60,
                      ),
                    ),
                    Text(
                      CurrencyUtils.formatCurrency(pendingAmount),
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: AppColors.warning,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      onTap: () => _showPrepaymentDialog(stats),
    );
  }
  /// 서비스 제공 카드
  Widget _buildServiceCard(Map<String, dynamic> stats) {
    final serviceCount = stats['serviceCount'] ?? 0;
    final serviceQuantity = stats['serviceQuantity'] ?? 0;
    final uniqueProducts = stats['uniqueServiceProducts'] ?? 0;

    return _buildMetricCard(
      title: '서비스 제공',
      icon: Icons.volunteer_activism_rounded,
      color: const Color(0xFF7C3AED), // 보라색
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${serviceCount}회',
            style: TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: Dimens.space8),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '총 수량',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 10,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    Text(
                      '${serviceQuantity}개',
                      style: const TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF7C3AED),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '상품 종류',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 10,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    Text(
                      '${uniqueProducts}종',
                      style: const TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF7C3AED),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      onTap: () => _showServiceDialog(stats),
    );
  }

  /// 판매자 성과 카드
  Widget _buildSellerCard(Map<String, dynamic> stats) {
    final sellerStats = stats['sellerStats'] as Map<String, Map<String, int>>;
    final topSeller = sellerStats.entries.isNotEmpty
        ? sellerStats.entries.reduce((a, b) =>
            (a.value['revenue'] ?? 0) > (b.value['revenue'] ?? 0) ? a : b)
        : null;

    return _buildMetricCard(
      title: '판매자 성과',
      icon: Icons.people_rounded,
      color: AppColors.accent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (topSeller != null) ...[
            Text(
              topSeller.key,
              style: const TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.onSurface,
              ),
            ),
            const SizedBox(height: Dimens.space4),
            Text(
              'TOP 판매자',
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 10,
                color: AppColors.neutral60,
              ),
            ),
            const SizedBox(height: Dimens.space8),
            Text(
              CurrencyUtils.formatCurrency(topSeller.value['revenue'] ?? 0),
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.accent,
              ),
            ),
          ] else ...[
            const Text(
              '데이터 없음',
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 16,
                color: AppColors.neutral60,
              ),
            ),
          ],
        ],
      ),
      onTap: () => _showSellerDialog(stats),
    );
  }

  /// 공통 메트릭 카드 빌더
  Widget _buildMetricCard({
    required String title,
    required IconData icon,
    required Color color,
    required Widget child,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(Dimens.radiusL),
      child: Container(
        padding: const EdgeInsets.all(Dimens.space20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(Dimens.radiusL),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(Dimens.space8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(Dimens.radiusS),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 20,
                  ),
                ),
                const SizedBox(width: Dimens.space12),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey.shade400,
                  size: 14,
                ),
              ],
            ),
            const SizedBox(height: Dimens.space16),
            child,
          ],
        ),
      ),
    );
  }









  /// 판매자별 성과 섹션 (모던한 디자인)
  Widget _buildSellerPerformanceSection(Map<String, dynamic> stats) {
    final sellerStats = stats['sellerStats'] as Map<String, Map<String, int>>;

    if (sellerStats.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(Dimens.space32),
          child: Text(
            '판매자별 통계 데이터가 없습니다',
            style: TextStyle(
              fontFamily: 'Pretendard',
              color: AppColors.neutral60,
              fontSize: 16,
            ),
          ),
        ),
      );
    }

    // 이름순으로 정렬 (순위 없이)
    final sortedSellers = sellerStats.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 헤더
        Padding(
          padding: const EdgeInsets.only(bottom: Dimens.space20),
          child: Text(
            '총 ${sortedSellers.length}명의 판매자',
            style: TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 14,
              color: AppColors.neutral60,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),

        // 판매자 리스트 (순위 없이)
        ...sortedSellers.map((entry) {
          final revenue = entry.value['revenue'] ?? 0;
          final count = entry.value['count'] ?? 0;

          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey.shade200,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // 판매자 정보
                Expanded(
                  child: Row(
                    children: [
                      // 판매자명
                      Expanded(
                        flex: 2,
                        child: Text(
                          entry.key,
                          style: const TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                            color: AppColors.onSurface,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                      // 매출 (중앙)
                      Expanded(
                        flex: 2,
                        child: Text(
                          CurrencyUtils.formatCurrency(revenue),
                          style: const TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: AppColors.onSurface,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      // 거래 건수 (우측)
                      Expanded(
                        flex: 1,
                        child: Text(
                          '${count}건',
                          style: const TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: AppColors.neutral60,
                          ),
                          textAlign: TextAlign.end,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  /// 선입금 현황 섹션
  Widget _buildPrepaymentStatusSection(Map<String, dynamic> stats) {
    return _buildSectionCard(
      title: '선입금 현황',
      icon: Icons.account_balance_outlined,
      child: Column(
        children: [
          _buildStatRow(
            '총 선입금 금액',
            CurrencyUtils.formatCurrency(stats['totalPrepaymentAmount']),
            Icons.savings_outlined,
            AppColors.info,
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '수령 완료',
            '${CurrencyUtils.formatCurrency(stats['receivedPrepaymentAmount'])} (${stats['receivedPrepaymentCount']}건)',
            Icons.check_circle_outline,
            AppColors.success,
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '미수령',
            '${CurrencyUtils.formatCurrency(stats['pendingPrepaymentAmount'])} (${stats['pendingPrepaymentCount']}건)',
            Icons.pending_outlined,
            AppColors.warning,
          ),
        ],
      ),
    );
  }

  /// 서비스 제공 분석 섹션
  Widget _buildServiceAnalysisSection(Map<String, dynamic> stats) {
    return _buildSectionCard(
      title: '서비스 제공 분석',
      icon: Icons.volunteer_activism_outlined,
      child: Column(
        children: [
          _buildStatRow(
            '서비스 제공 횟수',
            '${stats['serviceCount']}회',
            Icons.volunteer_activism_outlined,
            const Color(0xFF7C3AED),
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '총 서비스 수량',
            '${stats['serviceQuantity']}개',
            Icons.inventory_outlined,
            const Color(0xFF059669),
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '서비스 상품 종류',
            '${stats['uniqueServiceProducts']}종',
            Icons.category_outlined,
            const Color(0xFF2563EB),
          ),
        ],
      ),
    );
  }
  /// 공통 다이얼로그 표시 헬퍼 (반응형 큰 다이얼로그)
  Future<void> _showSectionDialog({
    required IconData icon,
    required String title,
    required Color color,
    required Widget content,
  }) async {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width >= 600;

    await showDialog(
      context: context,
      builder: (context) => custom_dialog.DialogTheme.buildResponsiveLargeDialog(
        child: Column(
          children: [
            // 헤더
            Container(
              padding: EdgeInsets.all(custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    icon,
                    color: color,
                    size: isTablet ? 28 : 24,
                  ),
                  SizedBox(width: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: isTablet ? 20 : 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.onSurface,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: AppColors.neutral60,
                      size: isTablet ? 24 : 20,
                    ),
                  ),
                ],
              ),
            ),

            // 내용
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),
                child: content,
              ),
            ),

            // 하단 버튼
            Container(
              padding: EdgeInsets.all(custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),
              child: Align(
                alignment: Alignment.centerRight,
                child: custom_dialog.DialogTheme.buildModernButton(
                  text: '닫기',
                  onPressed: () => Navigator.of(context).pop(),
                  isTablet: isTablet,
                  isPrimary: false,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }



  Future<void> _showSetAnalysisDialog(Map<String, dynamic> stats) async {
    await _showSectionDialog(
      icon: Icons.savings_rounded,
      title: '세트 분석 상세',
      color: AppColors.success,
      content: _buildSetAnalysisSection(stats),
    );
  }



  Future<void> _showSellerDialog(Map<String, dynamic> stats) async {
    await _showSectionDialog(
      icon: Icons.people_outlined,
      title: '판매자별 통계',
      color: AppColors.accent,
      content: _buildSellerPerformanceSection(stats),
    );
  }

  Future<void> _showPrepaymentDialog(Map<String, dynamic> stats) async {
    await _showSectionDialog(
      icon: Icons.account_balance_outlined,
      title: '선입금 현황 상세',
      color: AppColors.info,
      content: _buildPrepaymentStatusSection(stats),
    );
  }

  Future<void> _showServiceDialog(Map<String, dynamic> stats) async {
    await _showSectionDialog(
      icon: Icons.volunteer_activism_outlined,
      title: '서비스 제공 상세',
      color: const Color(0xFF7C3AED),
      content: _buildServiceAnalysisSection(stats),
    );
  }

  Future<void> _showProductAnalysisDialog(Map<String, dynamic> stats) async {
    await _showSectionDialog(
      icon: Icons.bar_chart_rounded,
      title: '상품별 매출 상세',
      color: const Color(0xFF059669),
      content: _buildProductAnalysisSection(stats),
    );
  }



  /// 공통 섹션 카드
  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(Dimens.space20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(Dimens.radiusL),
        border: Border.all(
          color: AppColors.neutral60.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.elevation1,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(Dimens.space8),
                decoration: BoxDecoration(
                  color: AppColors.primarySeed.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(Dimens.radiusS),
                ),
                child: Icon(
                  icon,
                  color: AppColors.primarySeed,
                  size: 20,
                ),
              ),
              const SizedBox(width: Dimens.space12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontFamily: 'Pretendard',
                  fontWeight: FontWeight.bold,
                  color: AppColors.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: Dimens.space16),
          child,
        ],
      ),
    );
  }

  // _buildEmptySection 메서드 제거됨 (사용하지 않음)

  /// 통계 행 위젯
  Widget _buildStatRow(String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(Dimens.space6),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(Dimens.radiusS),
          ),
          child: Icon(
            icon,
            size: 16,
            color: color,
          ),
        ),
        const SizedBox(width: Dimens.space12),
        Expanded(
          child: Text(
            label,
            style: const TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.onSurface,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontFamily: 'Pretendard',
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// 카테고리별 판매 상세 다이얼로그
  Future<void> _showCategoryDetailsDialog(Map<String, dynamic> stats) async {
    await _showSectionDialog(
      icon: Icons.pie_chart,
      title: '카테고리별 판매 상세',
      color: const Color(0xFF2563EB),
      content: _buildCategoryAnalysisSection(stats),
    );
  }

  // 상품별 매출 분석용 상태 변수들
  String _productSortOption = '판매금액순';
  bool _productSortAscending = false; // 판매금액순은 높은 수치부터 (내림차순)가 기본

  // 시간대별 매출 분석용 상태 변수들
  DateTime? _selectedDate;
  int _startHour = 10;
  int _endHour = 17;

  /// 상품별 매출 분석 섹션
  Widget _buildProductAnalysisSection(Map<String, dynamic> stats) {
    final productStats = stats['productStats'] as Map<String, Map<String, int>>;

    // 상품 데이터 정렬
    List<MapEntry<String, Map<String, int>>> sortedProducts = productStats.entries.toList();

    switch (_productSortOption) {
      case '이름순':
        sortedProducts.sort((a, b) => _productSortAscending ? a.key.compareTo(b.key) : b.key.compareTo(a.key));
        break;
      case '판매량순':
        sortedProducts.sort((a, b) => _productSortAscending
          ? (a.value['quantity'] ?? 0).compareTo(b.value['quantity'] ?? 0)
          : (b.value['quantity'] ?? 0).compareTo(a.value['quantity'] ?? 0));
        break;
      case '판매금액순':
      default:
        sortedProducts.sort((a, b) => _productSortAscending
          ? (a.value['revenue'] ?? 0).compareTo(b.value['revenue'] ?? 0)
          : (b.value['revenue'] ?? 0).compareTo(a.value['revenue'] ?? 0));
        break;
    }

    return SingleChildScrollView(
      child: Column(
        children: [
          // 헤더 - 제목과 총 상품 수를 한 줄에 배치
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 정렬 버튼들
              Row(
                children: [
                  _buildProductSortButton('판매금액순'),
                  const SizedBox(width: Dimens.space8),
                  _buildProductSortButton('판매량순'),
                  const SizedBox(width: Dimens.space8),
                  _buildProductSortButton('이름순'),
                ],
              ),
              // 총 상품 수
              Text(
                '총 ${sortedProducts.length}개 상품',
                style: TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: 14,
                  color: AppColors.neutral60,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: Dimens.space20),

          // 상품 리스트 (카드 간격 0, 라운드 제거)
          ...sortedProducts.map((entry) {
            final productName = entry.key;
            final quantity = entry.value['quantity'] ?? 0;
            final revenue = entry.value['revenue'] ?? 0;

            return Container(
              padding: const EdgeInsets.all(Dimens.space16),
              decoration: const BoxDecoration(
                color: Colors.white,
                border: Border(
                  bottom: BorderSide(
                    color: AppColors.neutral20,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  // 상품명 (가중치 3)
                  Expanded(
                    flex: 3,
                    child: Text(
                      productName,
                      style: const TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.onSurface,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // 판매 개수 (가중치 1)
                  Expanded(
                    flex: 1,
                    child: Text(
                      '${quantity}개',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 12,
                        color: AppColors.neutral60,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  // 총 판매액 (가중치 2)
                  Expanded(
                    flex: 2,
                    child: Text(
                      CurrencyUtils.formatCurrency(revenue),
                      style: const TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: AppColors.onSurface,
                      ),
                      textAlign: TextAlign.end,
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  /// 상품별 정렬 버튼
  Widget _buildProductSortButton(String label) {
    final isSelected = _productSortOption == label;
    return GestureDetector(
      onTap: () {
        setState(() {
          if (_productSortOption == label) {
            // 같은 옵션을 다시 누르면 오름차순/내림차순 토글
            _productSortAscending = !_productSortAscending;
          } else {
            // 다른 옵션을 누르면 해당 옵션으로 변경하고 내림차순으로 초기화
            _productSortOption = label;
            _productSortAscending = false; // 판매금액순과 판매량순은 내림차순이 기본
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: Dimens.space12,
          vertical: Dimens.space6,
        ),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF059669) : Colors.transparent,
          borderRadius: BorderRadius.circular(Dimens.radiusS),
          border: Border.all(
            color: isSelected ? const Color(0xFF059669) : AppColors.neutral30,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isSelected ? Colors.white : AppColors.neutral60,
              ),
            ),
            if (isSelected) ...[
              const SizedBox(width: 4),
              Icon(
                _productSortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                size: 12,
                color: Colors.white,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 시간대별 매출 데이터 생성
  List<ChartData> _generateTimeBasedData(Map<String, dynamic> stats) {
    final salesLogs = stats['salesLogs'] as List<SalesLog>? ?? [];

    // 선택된 날짜의 로그만 필터링
    final filteredLogs = salesLogs.where((log) {
      final logDate = DateTime.fromMillisecondsSinceEpoch(log.saleTimestamp);
      final logDateOnly = DateTime(logDate.year, logDate.month, logDate.day);
      final selectedDateOnly = DateTime(_selectedDate!.year, _selectedDate!.month, _selectedDate!.day);

      return logDateOnly.isAtSameMomentAs(selectedDateOnly);
    }).toList();

    // 시간대별 매출 집계 (설정된 시간 범위)
    final Map<int, int> hourlyRevenue = {};
    for (int hour = _startHour; hour <= _endHour; hour++) {
      hourlyRevenue[hour] = 0;
    }

    for (final log in filteredLogs) {
      // 판매와 서비스만 매출에 포함
      if (log.transactionType == TransactionType.sale || log.transactionType == TransactionType.service) {
        final logDate = DateTime.fromMillisecondsSinceEpoch(log.saleTimestamp);
        final hour = logDate.hour;

        // 설정된 시간 범위 내의 데이터만 집계
        if (hour >= _startHour && hour <= _endHour) {
          hourlyRevenue[hour] = (hourlyRevenue[hour] ?? 0) + log.totalAmount;
        }
      }
    }

    // 차트 데이터 생성
    final List<ChartData> data = [];
    for (int hour = _startHour; hour <= _endHour; hour++) {
      data.add(ChartData(
        '${hour}시',
        (hourlyRevenue[hour] ?? 0).toDouble(),
        const Color(0xFF7C3AED),
      ));
    }

    return data;
  }

  /// 시간대별 통계 요약
  Widget _buildTimeBasedSummary(Map<String, dynamic> stats) {
    final timeBasedData = _generateTimeBasedData(stats);

    if (timeBasedData.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(Dimens.space16),
        decoration: BoxDecoration(
          color: AppColors.neutral10,
          borderRadius: BorderRadius.circular(Dimens.radiusM),
        ),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: AppColors.neutral60,
              size: 16,
            ),
            const SizedBox(width: Dimens.space8),
            Expanded(
              child: Text(
                '선택된 기간에 매출 데이터가 없습니다.',
                style: TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: 12,
                  color: AppColors.neutral60,
                ),
              ),
            ),
          ],
        ),
      );
    }

    // 통계 계산
    final totalRevenue = timeBasedData.fold<double>(0, (sum, data) => sum + data.value);
    final peakHour = timeBasedData.reduce((a, b) => a.value > b.value ? a : b);
    final averageRevenue = totalRevenue / timeBasedData.length;

    return Column(
      children: [
        Text(
          '시간대별 매출 통계',
          style: TextStyle(
            fontFamily: 'Pretendard',
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.onSurface,
          ),
        ),
        const SizedBox(height: Dimens.space16),

        // 통계 카드들
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                '총 매출',
                CurrencyUtils.formatCurrency(totalRevenue.toInt()),
                Icons.attach_money,
                AppColors.success,
              ),
            ),
            const SizedBox(width: Dimens.space12),
            Expanded(
              child: _buildStatCard(
                '최고 시간대',
                peakHour.category,
                Icons.trending_up,
                const Color(0xFF7C3AED),
              ),
            ),
          ],
        ),
        const SizedBox(height: Dimens.space12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                '시간당 평균',
                CurrencyUtils.formatCurrency(averageRevenue.toInt()),
                Icons.schedule,
                AppColors.neutral60,
              ),
            ),
            const SizedBox(width: Dimens.space12),
            Expanded(
              child: _buildStatCard(
                '최고 매출',
                CurrencyUtils.formatCurrency(peakHour.value.toInt()),
                Icons.star,
                AppColors.warning,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 통계 카드 위젯
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(Dimens.space12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(Dimens.radiusS),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: Dimens.space4),
          Text(
            title,
            style: TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 11,
              color: AppColors.neutral60,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: Dimens.space2),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 날짜 포맷팅
  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}';
  }

  /// 시간대별 매출 설정 다이얼로그
  Future<void> _showTimeBasedSettings() async {
    final currentWorkspace = ref.read(unifiedWorkspaceProvider).currentWorkspace;
    if (currentWorkspace == null || _selectedDate == null) return;

    await showDialog(
      context: context,
      builder: (context) => _TimeBasedSettingsDialog(
        currentWorkspace: currentWorkspace,
        initialSelectedDate: _selectedDate!,
        initialStartHour: _startHour,
        initialEndHour: _endHour,
        onSettingsChanged: (date, startHour, endHour) {
          setState(() {
            _selectedDate = date;
            _startHour = startHour;
            _endHour = endHour;
          });
          // 설정 저장
          _saveTimeBasedSettings();
        },
      ),
    );
  }

  /// 시간대별 매출 분석 다이얼로그
  Future<void> _showTimeBasedAnalysisDialog(Map<String, dynamic> stats) async {
    await _showSectionDialog(
      icon: Icons.timeline_rounded,
      title: '시간대별 매출 상세',
      color: const Color(0xFF7C3AED),
      content: _buildTimeBasedAnalysisSection(stats),
    );
  }

  /// 시간대별 매출 분석 섹션
  Widget _buildTimeBasedAnalysisSection(Map<String, dynamic> stats) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // 날짜 범위 표시
          Container(
            padding: const EdgeInsets.all(Dimens.space16),
            decoration: BoxDecoration(
              color: AppColors.neutral10,
              borderRadius: BorderRadius.circular(Dimens.radiusM),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: AppColors.neutral60,
                  size: 20,
                ),
                const SizedBox(width: Dimens.space8),
                Text(
                  _selectedDate != null
                    ? '분석 날짜: ${_formatDate(_selectedDate!)} (${_startHour}시~${_endHour}시)'
                    : '날짜 설정 중...',
                  style: TextStyle(
                    fontFamily: 'Pretendard',
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.neutral60,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: Dimens.space20),

          // 시간대별 상세 차트
          SizedBox(
            height: 300,
            child: SfCartesianChart(
              primaryXAxis: CategoryAxis(
                labelStyle: const TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: 12,
                  color: Color.fromARGB(255, 0, 0, 0),
                ),
              ),
              primaryYAxis: NumericAxis(
                labelFormat: '{value}',
                axisLabelFormatter: (AxisLabelRenderDetails details) {
                  return ChartAxisLabel(
                    CurrencyUtils.formatCurrencyInManUnit(details.value.toInt()),
                    details.textStyle,
                  );
                },
                labelStyle: const TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: 12,
                  color: Color.fromARGB(255, 0, 0, 0),
                ),
              ),
              plotAreaBorderWidth: 0,
              series: <CartesianSeries>[
                LineSeries<ChartData, String>(
                  dataSource: _generateTimeBasedData(stats),
                  xValueMapper: (ChartData data, _) => data.category,
                  yValueMapper: (ChartData data, _) => data.value,
                  color: const Color(0xFF7C3AED),
                  width: 3,
                  markerSettings: const MarkerSettings(
                    isVisible: true,
                    height: 6,
                    width: 6,
                    color: Color(0xFF7C3AED),
                  ),
                  dataLabelSettings: DataLabelSettings(
                    isVisible: true,
                    textStyle: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey.shade700,
                    ),
                    labelAlignment: ChartDataLabelAlignment.top,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: Dimens.space20),

          // 시간대별 통계 요약
          _buildTimeBasedSummary(stats),
        ],
      ),
    );
  }



  /// 카테고리별 분석 섹션
  Widget _buildCategoryAnalysisSection(Map<String, dynamic> stats) {
    final categoryStats = stats['categoryStats'] as Map<String, Map<String, int>>;

    if (categoryStats.isEmpty) {
      return const SizedBox(
        height: 200,
        child: Center(
          child: Text(
            '카테고리별 판매 데이터가 없습니다',
            style: TextStyle(
              fontFamily: 'Pretendard',
              color: AppColors.neutral60,
            ),
          ),
        ),
      );
    }

    // 매출 기준으로 정렬
    final sortedCategories = categoryStats.entries.toList()
      ..sort((a, b) => (b.value['revenue'] ?? 0).compareTo(a.value['revenue'] ?? 0));

    final totalRevenue = categoryStats.values.fold<int>(0, (sum, stats) => sum + (stats['revenue'] ?? 0));
    final totalQuantity = categoryStats.values.fold<int>(0, (sum, stats) => sum + (stats['quantity'] ?? 0));

    return Column(
      children: [
        // 전체 요약
        Container(
          padding: const EdgeInsets.all(Dimens.space16),
          decoration: BoxDecoration(
            color: AppColors.neutral10,
            borderRadius: BorderRadius.circular(Dimens.radiusM),
          ),
          child: Column(
            children: [
              _buildStatRow(
                '총 카테고리 수',
                '${categoryStats.length}개',
                Icons.category_outlined,
                AppColors.primarySeed,
              ),
              const SizedBox(height: Dimens.space12),
              _buildStatRow(
                '총 매출',
                CurrencyUtils.formatCurrency(totalRevenue),
                Icons.account_balance_wallet_outlined,
                AppColors.success,
              ),
              const SizedBox(height: Dimens.space12),
              _buildStatRow(
                '총 판매량',
                '${totalQuantity}개',
                Icons.inventory_outlined,
                AppColors.info,
              ),
            ],
          ),
        ),
        const SizedBox(height: Dimens.space20),

        // 카테고리별 상세 목록
        ...sortedCategories.map((entry) {
          final categoryName = entry.key;
          final revenue = entry.value['revenue'] ?? 0;
          final quantity = entry.value['quantity'] ?? 0;
          final percentage = totalRevenue > 0 ? (revenue / totalRevenue * 100) : 0;

          return Container(
            margin: const EdgeInsets.only(bottom: 8), // 12 → 8로 줄임
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12), // 세로 패딩 줄임
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12), // 더 둥글게
              border: Border.all(color: AppColors.neutral30),
            ),
            child: Row(
              children: [
                // 카테고리 아이콘 (더 작게)
                Container(
                  padding: const EdgeInsets.all(6), // 8 → 6으로 줄임
                  decoration: BoxDecoration(
                    color: AppColors.primarySeed.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    Icons.category,
                    size: 14, // 16 → 14로 줄임
                    color: AppColors.primarySeed,
                  ),
                ),
                const SizedBox(width: 12),

                // 카테고리명
                Expanded(
                  flex: 2,
                  child: Text(
                    categoryName,
                    style: const TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 15, // 16 → 15로 줄임
                      fontWeight: FontWeight.w600,
                      color: AppColors.onSurface,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // 퍼센트 (중앙)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 3, // 4 → 3으로 줄임
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.success.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    '${percentage.toStringAsFixed(1)}%',
                    style: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 11, // 12 → 11로 줄임
                      fontWeight: FontWeight.w600,
                      color: AppColors.success,
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // 매출 (우측)
                Expanded(
                  flex: 2,
                  child: Text(
                    CurrencyUtils.formatCurrency(revenue),
                    style: const TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 14, // 16 → 14로 줄임
                      fontWeight: FontWeight.bold,
                      color: AppColors.onSurface,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                // 판매량 (우측 끝)
                Expanded(
                  flex: 1,
                  child: Text(
                    '${quantity}개',
                    style: const TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 13, // 16 → 13으로 줄임
                      fontWeight: FontWeight.w600,
                      color: AppColors.neutral60,
                    ),
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  /// 세트별 상세 정보 섹션
  Widget _buildSetDetailsSection(Map<String, dynamic> stats) {
    // 임시로 간단한 정보 표시 (실제로는 세트 정보를 분석해야 함)
    return Container(
      padding: const EdgeInsets.all(Dimens.space16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(Dimens.radiusM),
        border: Border.all(color: AppColors.neutral30),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.inventory_2_outlined,
                color: AppColors.primarySeed,
                size: 20,
              ),
              const SizedBox(width: Dimens.space8),
              Text(
                '세트별 판매 현황',
                style: const TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: Dimens.space16),

          // 세트 할인 적용 현황 표시
          _buildSetDiscountDetails(stats),
        ],
      ),
    );
  }

  /// 세트 할인 상세 내역 위젯 (엑셀 내보내기 방식과 동일)
  Widget _buildSetDiscountDetails(Map<String, dynamic> stats) {
    // 엑셀 내보내기와 완전히 동일한 방식으로 setDiscountStats 사용
    final setDiscountStats = stats['setDiscountStats'] as Map<String, Map<String, int>>? ?? {};

    print('🎯 세트 분석 다이얼로그 디버깅:');
    print('  setDiscountStats: ${setDiscountStats.length}개');
    print('  setDiscountStats 내용: $setDiscountStats');

    // 엑셀과 동일한 정렬 (할인액 기준 내림차순)
    final sortedSets = setDiscountStats.entries.toList()
      ..sort((a, b) => (b.value['totalDiscount'] ?? 0).compareTo(a.value['totalDiscount'] ?? 0));

    // 세트 할인 데이터가 있는 경우
    if (sortedSets.isNotEmpty) {
      // 총 할인 정보 계산 (엑셀과 동일)
      final totalCount = sortedSets.fold<int>(0, (sum, entry) => sum + (entry.value['count'] ?? 0));
      final totalDiscount = sortedSets.fold<int>(0, (sum, entry) => sum + (entry.value['totalDiscount'] ?? 0));

      return Column(
        children: [
          // 세트 할인 요약 (엑셀과 동일한 제목)
          Container(
            padding: const EdgeInsets.all(Dimens.space12),
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(Dimens.radiusS),
              border: Border.all(color: AppColors.success.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.discount_outlined,
                  color: AppColors.success,
                  size: 20,
                ),
                const SizedBox(width: Dimens.space8),
                Expanded(
                  child: Text(
                    '💰 세트 판매 상세 (총 ${sortedSets.length}개 세트)',
                    style: const TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppColors.success,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: Dimens.space12),

          // 세트별 판매 현황 테이블 (엑셀과 동일한 형식)
          Container(
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(Dimens.radiusS),
              border: Border.all(color: AppColors.neutral30),
            ),
            child: Column(
              children: [
                // 테이블 헤더 (엑셀과 동일)
                Container(
                  padding: const EdgeInsets.all(Dimens.space12),
                  decoration: BoxDecoration(
                    color: AppColors.primarySeed.withValues(alpha: 0.1),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(Dimens.radiusS),
                      topRight: Radius.circular(Dimens.radiusS),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: Text(
                          '세트명',
                          style: const TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: AppColors.onSurface,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          '적용 횟수',
                          style: const TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: AppColors.onSurface,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          '총 할인액',
                          style: const TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: AppColors.onSurface,
                          ),
                          textAlign: TextAlign.end,
                        ),
                      ),
                    ],
                  ),
                ),

                // 세트별 데이터 행들 (엑셀과 동일한 정렬)
                ...sortedSets.map((setEntry) {
                  final setName = setEntry.key;
                  final count = setEntry.value['count'] ?? 0;
                  final discount = setEntry.value['totalDiscount'] ?? 0;

                  return Container(
                    padding: const EdgeInsets.all(Dimens.space12),
                    decoration: const BoxDecoration(
                      border: Border(
                        bottom: BorderSide(color: AppColors.neutral20, width: 0.5),
                      ),
                    ),
                    child: Row(
                      children: [
                        // 세트명
                        Expanded(
                          flex: 3,
                          child: Text(
                            setName,
                            style: const TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              color: AppColors.onSurface,
                            ),
                          ),
                        ),
                        // 적용 횟수
                        Expanded(
                          flex: 2,
                          child: Text(
                            '${count}회',
                            style: const TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              color: AppColors.onSurface,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        // 총 할인액
                        Expanded(
                          flex: 2,
                          child: Text(
                            CurrencyUtils.formatCurrency(discount),
                            style: const TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                              color: AppColors.success,
                            ),
                            textAlign: TextAlign.end,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),

                // 총계 행 (엑셀과 동일)
                Container(
                  padding: const EdgeInsets.all(Dimens.space12),
                  decoration: BoxDecoration(
                    color: AppColors.primarySeed.withValues(alpha: 0.05),
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(Dimens.radiusS),
                      bottomRight: Radius.circular(Dimens.radiusS),
                    ),
                  ),
                  child: Row(
                    children: [
                      // 총계 라벨
                      Expanded(
                        flex: 3,
                        child: Text(
                          '총계',
                          style: const TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: 14,
                            fontWeight: FontWeight.w700,
                            color: AppColors.onSurface,
                          ),
                        ),
                      ),
                      // 총 적용 횟수
                      Expanded(
                        flex: 2,
                        child: Text(
                          '${totalCount}회',
                          style: const TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: 14,
                            fontWeight: FontWeight.w700,
                            color: AppColors.onSurface,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      // 총 할인액
                      Expanded(
                        flex: 2,
                        child: Text(
                          CurrencyUtils.formatCurrency(totalDiscount),
                          style: const TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: 14,
                            fontWeight: FontWeight.w700,
                            color: AppColors.success,
                          ),
                          textAlign: TextAlign.end,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    } else {
      // 세트 할인이 없는 경우
      return Container(
        padding: const EdgeInsets.all(Dimens.space12),
        decoration: BoxDecoration(
          color: AppColors.neutral10,
          borderRadius: BorderRadius.circular(Dimens.radiusS),
        ),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: AppColors.neutral60,
              size: 16,
            ),
            const SizedBox(width: Dimens.space8),
            Expanded(
              child: Text(
                '선택된 기간에 적용된 세트 할인이 없습니다.',
                style: TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: 12,
                  color: AppColors.neutral60,
                ),
              ),
            ),
          ],
        ),
      );
    }
  }


}

/// 시간대별 매출 설정 다이얼로그
class _TimeBasedSettingsDialog extends StatefulWidget {
  final EventWorkspace currentWorkspace;
  final DateTime initialSelectedDate;
  final int initialStartHour;
  final int initialEndHour;
  final Function(DateTime date, int startHour, int endHour) onSettingsChanged;

  const _TimeBasedSettingsDialog({
    required this.currentWorkspace,
    required this.initialSelectedDate,
    required this.initialStartHour,
    required this.initialEndHour,
    required this.onSettingsChanged,
  });

  @override
  State<_TimeBasedSettingsDialog> createState() => _TimeBasedSettingsDialogState();
}

class _TimeBasedSettingsDialogState extends State<_TimeBasedSettingsDialog> {
  late DateTime _selectedDate;
  late int _startHour;
  late int _endHour;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialSelectedDate;
    _startHour = widget.initialStartHour;
    _endHour = widget.initialEndHour;
  }

  // 행사 기간 내 날짜 목록 생성
  List<DateTime> _getAvailableDates() {
    final List<DateTime> dates = [];
    DateTime current = widget.currentWorkspace.startDate;
    final end = widget.currentWorkspace.endDate;

    while (current.isBefore(end) || current.isAtSameMomentAs(end)) {
      dates.add(current);
      current = current.add(const Duration(days: 1));
    }

    return dates;
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width >= 600;
    final availableDates = _getAvailableDates();

    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      insetPadding: EdgeInsets.symmetric(
        horizontal: isTablet ? 40.0 : 24.0,
        vertical: isTablet ? 24.0 : 16.0,
      ),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: isTablet ? 400 : 350,
          maxHeight: screenSize.height * 0.6,
        ),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.all(isTablet ? 24.0 : 20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 헤더
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF7C3AED).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.timeline_rounded,
                      color: Color(0xFF7C3AED),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      '시간대별 매출 설정',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: isTablet ? 18 : 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.onSurface,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: AppColors.neutral60,
                      size: isTablet ? 24 : 20,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // 날짜 선택
              _buildDateSelector(availableDates),

              const SizedBox(height: 20),

              // 시간 범위 선택
              _buildTimeRangeSelector(),

              const SizedBox(height: 24),

              // 버튼
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: isTablet ? 16 : 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(color: AppColors.neutral30),
                        ),
                      ),
                      child: Text(
                        '취소',
                        style: TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: isTablet ? 16 : 14,
                          fontWeight: FontWeight.w500,
                          color: AppColors.neutral60,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        widget.onSettingsChanged(_selectedDate, _startHour, _endHour);
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF7C3AED),
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: isTablet ? 16 : 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        '적용',
                        style: TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: isTablet ? 16 : 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 날짜 선택기
  Widget _buildDateSelector(List<DateTime> availableDates) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '분석 날짜 선택',
          style: TextStyle(
            fontFamily: 'Pretendard',
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.neutral60,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.neutral30),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<DateTime>(
              value: _selectedDate,
              isExpanded: true,
              menuMaxHeight: 200, // 고정 높이로 스크롤 가능
              items: availableDates.map((date) {
                return DropdownMenuItem<DateTime>(
                  value: date,
                  child: Text(
                    '${date.month}/${date.day} (${_getDayOfWeek(date)})',
                    style: const TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 14,
                      color: AppColors.onSurface,
                    ),
                  ),
                );
              }).toList(),
              onChanged: (DateTime? newDate) {
                if (newDate != null) {
                  setState(() {
                    _selectedDate = newDate;
                  });
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  /// 시간 범위 선택기
  Widget _buildTimeRangeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '분석 시간 범위',
          style: TextStyle(
            fontFamily: 'Pretendard',
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.neutral60,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            // 시작 시간
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '시작 시간',
                    style: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 12,
                      color: AppColors.neutral60,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.neutral30),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<int>(
                        value: _startHour,
                        isExpanded: true,
                        menuMaxHeight: 200, // 고정 높이로 스크롤 가능
                        items: List.generate(24, (index) {
                          return DropdownMenuItem<int>(
                            value: index,
                            child: Text(
                              '${index}시',
                              style: const TextStyle(
                                fontFamily: 'Pretendard',
                                fontSize: 14,
                                color: AppColors.onSurface,
                              ),
                            ),
                          );
                        }),
                        onChanged: (int? newHour) {
                          if (newHour != null && newHour < _endHour) {
                            setState(() {
                              _startHour = newHour;
                            });
                          }
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            // 종료 시간
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '종료 시간',
                    style: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 12,
                      color: AppColors.neutral60,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.neutral30),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<int>(
                        value: _endHour,
                        isExpanded: true,
                        menuMaxHeight: 200, // 고정 높이로 스크롤 가능
                        items: List.generate(24, (index) {
                          return DropdownMenuItem<int>(
                            value: index,
                            child: Text(
                              '${index}시',
                              style: const TextStyle(
                                fontFamily: 'Pretendard',
                                fontSize: 14,
                                color: AppColors.onSurface,
                              ),
                            ),
                          );
                        }),
                        onChanged: (int? newHour) {
                          if (newHour != null && newHour > _startHour) {
                            setState(() {
                              _endHour = newHour;
                            });
                          }
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 요일 문자열 반환
  String _getDayOfWeek(DateTime date) {
    const weekdays = ['월', '화', '수', '목', '금', '토', '일'];
    return weekdays[date.weekday - 1];
  }
}
