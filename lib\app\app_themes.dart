import 'package:flutter/material.dart';
import '../utils/app_colors.dart';

/// 분리된 라이트/다크 Theme 정의 (기존 main.dart 에서 이전)
class AppThemes {
  /// Material Design 3 Expressive 2025 라이트 테마
  ///
  /// 2025년 트렌드를 반영한 현대적인 디자인:
  /// - 적당한 곡률 (12-20px)
  /// - 부드러운 그림자와 elevation
  /// - 일관된 색상 체계
  /// - 접근성을 고려한 타이포그래피
  /// - Pretendard 폰트 적용
  static ThemeData light() => ThemeData(
        useMaterial3: true,
        colorScheme: AppColors.lightColorScheme,
        fontFamily: 'Pretendard', // 테스트용: 매우 눈에 띄는 변화

        // 현대적인 Typography 시스템 (2025 트렌드) - 모든 스타일에 폰트 적용
        textTheme: const TextTheme(
          displayLarge: TextStyle(
            fontSize: 57,
            fontWeight: FontWeight.w400,
            letterSpacing: -0.25,
            height: 1.12,
            fontFamily: 'Pretendard', // 테스트용
          ),
          displayMedium: TextStyle(fontSize: 45, fontWeight: FontWeight.w400, letterSpacing: 0, height: 1.16, fontFamily: 'Pretendard'),
          displaySmall: TextStyle(fontSize: 36, fontWeight: FontWeight.w400, letterSpacing: 0, height: 1.22, fontFamily: 'Pretendard'),
          headlineLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.w400, letterSpacing: 0, height: 1.25, fontFamily: 'Pretendard'),
          headlineMedium: TextStyle(fontSize: 28, fontWeight: FontWeight.w400, letterSpacing: 0, height: 1.29, fontFamily: 'Pretendard'),
          headlineSmall: TextStyle(fontSize: 24, fontWeight: FontWeight.w400, letterSpacing: 0, height: 1.33, fontFamily: 'Pretendard'),
          titleLarge: TextStyle(fontSize: 22, fontWeight: FontWeight.w500, letterSpacing: 0, height: 1.27, fontFamily: 'Pretendard'),
          titleMedium: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, letterSpacing: 0.15, height: 1.50, fontFamily: 'Pretendard'),
          titleSmall: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, letterSpacing: 0.1, height: 1.43, fontFamily: 'Pretendard'),
          bodyLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w400, letterSpacing: 0.5, height: 1.50, fontFamily: 'Pretendard'),
          bodyMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w400, letterSpacing: 0.25, height: 1.43, fontFamily: 'Pretendard'),
          bodySmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w400, letterSpacing: 0.4, height: 1.33, fontFamily: 'Pretendard'),
          labelLarge: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, letterSpacing: 0.1, height: 1.43, fontFamily: 'Pretendard'),
          labelMedium: TextStyle(fontSize: 12, fontWeight: FontWeight.w500, letterSpacing: 0.5, height: 1.33, fontFamily: 'Pretendard'),
          labelSmall: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, letterSpacing: 0.5, height: 1.45, fontFamily: 'Pretendard'),
        ),
        appBarTheme: const AppBarTheme(
          centerTitle: false,
          elevation: 0,
          scrolledUnderElevation: 2,
          backgroundColor: AppColors.primarySeed,
          foregroundColor: AppColors.onPrimary,
          titleTextStyle: TextStyle(color: AppColors.onPrimary, fontSize: 22, fontWeight: FontWeight.w500),
          iconTheme: IconThemeData(color: AppColors.onPrimary, size: 24),
          actionsIconTheme: IconThemeData(color: AppColors.onPrimary, size: 24),
        ),
        cardTheme: CardThemeData(
          elevation: 1,
          shadowColor: AppColors.elevation1,
          surfaceTintColor: AppColors.surfaceTint,
          margin: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          clipBehavior: Clip.antiAlias,
        ),
        floatingActionButtonTheme: FloatingActionButtonThemeData(
          backgroundColor: AppColors.primarySeed,
          foregroundColor: AppColors.onPrimary,
          elevation: 6,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        ),
        inputDecorationTheme: InputDecorationTheme(
          filled: true,
          fillColor: AppColors.surfaceVariant,
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: BorderSide.none),
          enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: BorderSide(color: AppColors.neutral30)),
          focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: const BorderSide(color: AppColors.primarySeed, width: 2)),
          errorBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: const BorderSide(color: AppColors.error)),
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          labelStyle: const TextStyle(color: AppColors.onSurfaceVariant, fontSize: 14),
          hintStyle: TextStyle(color: AppColors.onSurfaceVariant.withValues(alpha: 0.6), fontSize: 14),
        ),
        dividerTheme: const DividerThemeData(color: AppColors.neutral30, thickness: 1, space: 1),
        snackBarTheme: SnackBarThemeData(
          backgroundColor: AppColors.neutral80,
          contentTextStyle: const TextStyle(color: AppColors.neutral0, fontSize: 14),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          behavior: SnackBarBehavior.floating,
          elevation: 6,
        ),
        tabBarTheme: TabBarThemeData(
          labelColor: AppColors.onPrimary,
            unselectedLabelColor: AppColors.onPrimary.withValues(alpha: 0.7),
            indicatorColor: AppColors.onPrimary,
            indicatorSize: TabBarIndicatorSize.tab,
            labelStyle: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14, letterSpacing: 0.1),
            unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14, letterSpacing: 0.1),
            overlayColor: WidgetStateProperty.all(AppColors.onPrimary.withValues(alpha: 0.12)),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            elevation: 1,
            shadowColor: AppColors.elevation1,
            surfaceTintColor: AppColors.surfaceTint,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            textStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, letterSpacing: 0.1),
          ),
        ),
        filledButtonTheme: FilledButtonThemeData(
          style: FilledButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            textStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, letterSpacing: 0.1),
          ),
        ),
        outlinedButtonTheme: OutlinedButtonThemeData(
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            side: const BorderSide(color: AppColors.neutral60),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            textStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, letterSpacing: 0.1),
          ),
        ),
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            textStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, letterSpacing: 0.1),
          ),
        ),
        drawerTheme: const DrawerThemeData(
          backgroundColor: AppColors.surface,
          surfaceTintColor: AppColors.surfaceTint,
          elevation: 16,
          shadowColor: AppColors.elevation4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(topRight: Radius.circular(16), bottomRight: Radius.circular(16)),
          ),
        ),
        chipTheme: ChipThemeData(
          backgroundColor: AppColors.surfaceVariant,
          deleteIconColor: AppColors.onSurfaceVariant,
          disabledColor: AppColors.neutral20,
          selectedColor: AppColors.secondaryLight,
          secondarySelectedColor: AppColors.secondary,
          shadowColor: AppColors.elevation1,
          labelStyle: const TextStyle(color: AppColors.onSurfaceVariant, fontSize: 14, fontWeight: FontWeight.w500),
          secondaryLabelStyle: const TextStyle(color: AppColors.onSecondary, fontSize: 14, fontWeight: FontWeight.w500),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        progressIndicatorTheme: const ProgressIndicatorThemeData(
          color: AppColors.primarySeed,
          linearTrackColor: AppColors.neutral30,
          circularTrackColor: AppColors.neutral30,
          refreshBackgroundColor: AppColors.surface,
        ),
  dialogTheme: DialogThemeData(
          backgroundColor: AppColors.surface,
          surfaceTintColor: AppColors.surfaceTint,
          elevation: 24,
          shadowColor: AppColors.elevation4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          titleTextStyle: const TextStyle(color: AppColors.onSurface, fontSize: 20, fontWeight: FontWeight.w600),
          contentTextStyle: const TextStyle(color: AppColors.onSurfaceVariant, fontSize: 14, fontWeight: FontWeight.w400),
        ),
        bottomSheetTheme: const BottomSheetThemeData(
          backgroundColor: AppColors.surface,
          surfaceTintColor: AppColors.surfaceTint,
          elevation: 16,
          shadowColor: AppColors.elevation4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
          ),
          clipBehavior: Clip.antiAlias,
        ),
        popupMenuTheme: PopupMenuThemeData(
          color: AppColors.surface,
          surfaceTintColor: AppColors.surfaceTint,
          elevation: 8,
          shadowColor: AppColors.elevation3,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          textStyle: const TextStyle(color: AppColors.onSurface, fontSize: 14, fontWeight: FontWeight.w400),
        ),
        switchTheme: SwitchThemeData(
          thumbColor: WidgetStateProperty.resolveWith((states) => states.contains(WidgetState.selected) ? AppColors.primarySeed : AppColors.neutral60),
          trackColor: WidgetStateProperty.resolveWith((states) => states.contains(WidgetState.selected) ? AppColors.primarySeed.withValues(alpha: 0.5) : AppColors.neutral30),
        ),
        checkboxTheme: CheckboxThemeData(
          fillColor: WidgetStateProperty.resolveWith((states) => states.contains(WidgetState.selected) ? AppColors.primarySeed : Colors.transparent),
          checkColor: WidgetStateProperty.all(AppColors.onPrimary),
          side: const BorderSide(color: AppColors.neutral60, width: 2),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        ),
        radioTheme: RadioThemeData(
          fillColor: WidgetStateProperty.resolveWith((states) => states.contains(WidgetState.selected) ? AppColors.primarySeed : AppColors.neutral60),
        ),
      );

  static ThemeData dark() => ThemeData(
        useMaterial3: true,
        colorScheme: AppColors.darkColorScheme,
        fontFamily: 'Pretendard',
        textTheme: const TextTheme(
          displayLarge: TextStyle(fontFamily: 'Pretendard'),
          displayMedium: TextStyle(fontFamily: 'Pretendard'),
          displaySmall: TextStyle(fontFamily: 'Pretendard'),
          headlineLarge: TextStyle(fontFamily: 'Pretendard'),
          headlineMedium: TextStyle(fontFamily: 'Pretendard'),
          headlineSmall: TextStyle(fontFamily: 'Pretendard'),
          titleLarge: TextStyle(fontFamily: 'Pretendard'),
          titleMedium: TextStyle(fontFamily: 'Pretendard'),
          titleSmall: TextStyle(fontFamily: 'Pretendard'),
          bodyLarge: TextStyle(fontFamily: 'Pretendard'),
          bodyMedium: TextStyle(fontFamily: 'Pretendard'),
          bodySmall: TextStyle(fontFamily: 'Pretendard'),
          labelLarge: TextStyle(fontFamily: 'Pretendard'),
          labelMedium: TextStyle(fontFamily: 'Pretendard'),
          labelSmall: TextStyle(fontFamily: 'Pretendard'),
        ),
        appBarTheme: const AppBarTheme(
          centerTitle: false,
          elevation: 0,
          scrolledUnderElevation: 2,
          backgroundColor: AppColors.primaryDark,
          foregroundColor: AppColors.onPrimary,
          titleTextStyle: TextStyle(color: AppColors.onPrimary, fontSize: 22, fontWeight: FontWeight.w500, fontFamily: 'Pretendard'),
          iconTheme: IconThemeData(color: AppColors.onPrimary, size: 24),
          actionsIconTheme: IconThemeData(color: AppColors.onPrimary, size: 24),
        ),
        cardTheme: CardThemeData(
          elevation: 1,
          shadowColor: AppColors.elevation1,
          surfaceTintColor: AppColors.primaryLight,
          margin: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          clipBehavior: Clip.antiAlias,
        ),
        floatingActionButtonTheme: FloatingActionButtonThemeData(
          backgroundColor: AppColors.primaryDark,
          foregroundColor: AppColors.onPrimary,
          elevation: 6,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        ),
        inputDecorationTheme: InputDecorationTheme(
          filled: true,
            fillColor: AppColors.surfaceVariant,
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: BorderSide.none),
            enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: BorderSide(color: AppColors.neutral30)),
            focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: const BorderSide(color: AppColors.primaryDark, width: 2)),
            errorBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: const BorderSide(color: AppColors.error)),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            labelStyle: const TextStyle(color: AppColors.onSurfaceVariant, fontSize: 14),
            hintStyle: TextStyle(color: AppColors.onSurfaceVariant.withValues(alpha: 0.6), fontSize: 14),
        ),
        dividerTheme: const DividerThemeData(color: AppColors.neutral30, thickness: 1, space: 1),
        snackBarTheme: SnackBarThemeData(
          backgroundColor: AppColors.neutral80,
          contentTextStyle: const TextStyle(color: AppColors.neutral0, fontSize: 14),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          behavior: SnackBarBehavior.floating,
          elevation: 6,
        ),
        tabBarTheme: TabBarThemeData(
          labelColor: AppColors.onPrimary,
          unselectedLabelColor: AppColors.onPrimary.withValues(alpha: 0.7),
          indicatorColor: AppColors.onPrimary,
          indicatorSize: TabBarIndicatorSize.tab,
          labelStyle: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14, letterSpacing: 0.1),
          unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14, letterSpacing: 0.1),
          overlayColor: WidgetStateProperty.all(AppColors.onPrimary.withValues(alpha: 0.12)),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            elevation: 1,
            shadowColor: AppColors.elevation1,
            surfaceTintColor: AppColors.primaryLight,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            textStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, letterSpacing: 0.1),
          ),
        ),
        filledButtonTheme: FilledButtonThemeData(
          style: FilledButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            textStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, letterSpacing: 0.1),
          ),
        ),
        outlinedButtonTheme: OutlinedButtonThemeData(
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            side: const BorderSide(color: AppColors.neutral60),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            textStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, letterSpacing: 0.1),
          ),
        ),
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            textStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, letterSpacing: 0.1),
          ),
        ),
        drawerTheme: const DrawerThemeData(
          backgroundColor: AppColors.surface,
          surfaceTintColor: AppColors.primaryLight,
          elevation: 16,
          shadowColor: AppColors.elevation4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(topRight: Radius.circular(16), bottomRight: Radius.circular(16)),
          ),
        ),
        chipTheme: ChipThemeData(
          backgroundColor: AppColors.surfaceVariant,
          deleteIconColor: AppColors.onSurfaceVariant,
          disabledColor: AppColors.neutral20,
          selectedColor: AppColors.secondaryLight,
          secondarySelectedColor: AppColors.secondary,
          shadowColor: AppColors.elevation1,
          labelStyle: const TextStyle(color: AppColors.onSurfaceVariant, fontSize: 14, fontWeight: FontWeight.w500),
          secondaryLabelStyle: const TextStyle(color: AppColors.onSecondary, fontSize: 14, fontWeight: FontWeight.w500),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        progressIndicatorTheme: const ProgressIndicatorThemeData(
          color: AppColors.primarySeed,
          linearTrackColor: AppColors.neutral30,
          circularTrackColor: AppColors.neutral30,
          refreshBackgroundColor: AppColors.surface,
        ),
  dialogTheme: DialogThemeData(
          backgroundColor: AppColors.surface,
          surfaceTintColor: AppColors.primaryLight,
          elevation: 24,
          shadowColor: AppColors.elevation4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          titleTextStyle: const TextStyle(color: AppColors.onSurface, fontSize: 20, fontWeight: FontWeight.w600),
          contentTextStyle: const TextStyle(color: AppColors.onSurfaceVariant, fontSize: 14, fontWeight: FontWeight.w400),
        ),
        bottomSheetTheme: const BottomSheetThemeData(
          backgroundColor: AppColors.surface,
          surfaceTintColor: AppColors.primaryLight,
          elevation: 16,
          shadowColor: AppColors.elevation4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
          ),
          clipBehavior: Clip.antiAlias,
        ),
        popupMenuTheme: PopupMenuThemeData(
          color: AppColors.surface,
          surfaceTintColor: AppColors.primaryLight,
          elevation: 8,
          shadowColor: AppColors.elevation3,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          textStyle: const TextStyle(color: AppColors.onSurface, fontSize: 14, fontWeight: FontWeight.w400),
        ),
        switchTheme: SwitchThemeData(
          thumbColor: WidgetStateProperty.resolveWith((states) => states.contains(WidgetState.selected) ? AppColors.primarySeed : AppColors.neutral60),
          trackColor: WidgetStateProperty.resolveWith((states) => states.contains(WidgetState.selected) ? AppColors.primarySeed.withValues(alpha: 0.5) : AppColors.neutral30),
        ),
        checkboxTheme: CheckboxThemeData(
          fillColor: WidgetStateProperty.resolveWith((states) => states.contains(WidgetState.selected) ? AppColors.primarySeed : Colors.transparent),
          checkColor: WidgetStateProperty.all(AppColors.onPrimary),
          side: const BorderSide(color: AppColors.neutral60, width: 2),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        ),
        radioTheme: RadioThemeData(
          fillColor: WidgetStateProperty.resolveWith((states) => states.contains(WidgetState.selected) ? AppColors.primarySeed : AppColors.neutral60),
        ),
      );
}
