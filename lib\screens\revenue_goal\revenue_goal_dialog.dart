import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../models/revenue_goal.dart';
import '../../models/seller.dart';
import '../../models/event.dart';
import '../../providers/revenue_goal_provider.dart';
import '../../providers/seller_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../providers/event_provider.dart';
import '../../utils/app_colors.dart';
import '../../utils/toast_utils.dart';
import '../../utils/dialog_theme.dart' as custom_dialog;
import '../../widgets/unsaved_changes_dialog.dart';

/// 목표 수익 관리 다이얼로그 (전체/판매자별 모드 분리)
class RevenueGoalDialog extends ConsumerStatefulWidget {
  const RevenueGoalDialog({super.key});

  @override
  ConsumerState<RevenueGoalDialog> createState() => _RevenueGoalDialogState();
}

class _RevenueGoalDialogState extends ConsumerState<RevenueGoalDialog> {
  final Map<String, bool> _expandedSellers = {};
  final Map<String, Map<String, TextEditingController>> _controllers = {};
  final Map<String, Map<String, String>> _originalValues = {}; // 원본 값 저장용

  /// 목표 수익 모드 변경
  Future<void> _changeRevenueGoalMode(RevenueGoalMode newMode) async {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) return;

    // 모드 변경 확인 다이얼로그
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('목표 관리 방식 변경'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              newMode == RevenueGoalMode.overall
                  ? '전체 목표 모드로 변경하시겠습니까?'
                  : '판매자별 목표 모드로 변경하시겠습니까?',
            ),
            const SizedBox(height: 8),
            Text(
              '기존에 설정된 목표 데이터는 유지되지만, 선택한 모드의 데이터만 사용됩니다.',
              style: TextStyle(
                fontSize: 12,
                color: AppColors.secondaryTextColor,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('변경'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // Event 업데이트
      final eventProvider = ref.read(eventNotifierProvider.notifier);
      final updatedEvent = currentWorkspace.toEvent().copyWith(
        revenueGoalMode: newMode,
        updatedAt: DateTime.now(),
      );
      await eventProvider.updateEvent(updatedEvent);
    }
  }

  @override
  void dispose() {
    // 모든 컨트롤러 정리
    for (final sellerControllers in _controllers.values) {
      for (final controller in sellerControllers.values) {
        controller.dispose();
      }
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final revenueGoalState = ref.watch(revenueGoalNotifierProvider);
    final sellers = ref.watch(sellerNotifierProvider).sellers;
    final currentWorkspace = ref.watch(currentWorkspaceProvider);

    if (currentWorkspace == null) {
      return AlertDialog(
        title: const Text('오류'),
        content: const Text('현재 선택된 행사가 없습니다.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('확인'),
          ),
        ],
      );
    }



    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          await _onBackPressed();
        }
      },
      child: custom_dialog.DialogTheme.buildResponsiveLargeDialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.95,
          height: MediaQuery.of(context).size.height * 0.85,
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(16), // 상하 일관된 라운딩
          ),
          child: Column(
            children: [
              // 헤더 (체크리스트 편집 다이얼로그 스타일)
              Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back, color: AppColors.onSurfaceVariant),
                      onPressed: _onBackPressed,
                      tooltip: '뒤로가기',
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '목표 수익 관리',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: AppColors.onSurface,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.check, color: AppColors.primarySeed),
                      onPressed: _saveAllChanges,
                      tooltip: '저장',
                    ),
                  ],
                ),
              ),

              // 모드 토글 버튼
              Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '목표 관리 방식',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.primaryTextColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: SegmentedButton<RevenueGoalMode>(
                            segments: const [
                              ButtonSegment<RevenueGoalMode>(
                                value: RevenueGoalMode.overall,
                                label: Text('전체 목표'),
                                icon: Icon(Icons.groups),
                              ),
                              ButtonSegment<RevenueGoalMode>(
                                value: RevenueGoalMode.seller,
                                label: Text('판매자별'),
                                icon: Icon(Icons.person),
                              ),
                            ],
                            selected: {currentWorkspace.revenueGoalMode},
                            onSelectionChanged: (Set<RevenueGoalMode> newSelection) {
                              _changeRevenueGoalMode(newSelection.first);
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      currentWorkspace.revenueGoalMode == RevenueGoalMode.overall
                          ? '행사 전체의 목표 수익을 날짜별로 설정합니다.'
                          : '각 판매자별로 목표 수익을 날짜별로 설정합니다.',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.secondaryTextColor,
                      ),
                    ),
                  ],
                ),
              ),

              // 컨텐츠
              Expanded(
                child: revenueGoalState.isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _buildGoalsList(sellers, revenueGoalState.goals),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 목표 목록 UI (모드에 따라 다르게 표시)
  Widget _buildGoalsList(List<Seller> sellers, List<RevenueGoal> goals) {
    final currentWorkspace = ref.read(currentWorkspaceProvider)!;
    final eventDates = _getEventDateRange(currentWorkspace);

    if (currentWorkspace.revenueGoalMode == RevenueGoalMode.overall) {
      // 전체 모드: 날짜별 전체 목표만 표시
      return _buildOverallGoalsList(eventDates, goals);
    } else {
      // 판매자별 모드: 판매자별로 접을 수 있는 섹션
      return _buildSellerGoalsList(sellers, eventDates, goals);
    }
  }

  /// 전체 목표 목록 UI
  Widget _buildOverallGoalsList(List<DateTime> eventDates, List<RevenueGoal> goals) {
    final overallGoals = goals.where((g) => g.sellerId == null).toList();
    final totalTarget = overallGoals.fold<double>(0, (sum, goal) => sum + goal.targetAmount);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 총 목표 수익 표시
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primarySeed.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: LayoutBuilder(
              builder: (context, constraints) {
                final isNarrow = constraints.maxWidth < 400;

                if (isNarrow) {
                  // 모바일: 목표/현재 수익 나란히, 달성률 다음 줄
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: _buildInfoColumn('총 목표 수익', '₩ ${NumberFormat('#,###').format(totalTarget)}'),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Consumer(
                              builder: (context, ref, child) {
                                final revenueStats = ref.watch(revenueGoalStatsProvider);
                                return _buildInfoColumn(
                                  '현재 수익',
                                  '₩ ${NumberFormat('#,###').format(revenueStats.totalActual.toInt())}'
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Consumer(
                        builder: (context, ref, child) {
                          final revenueStats = ref.watch(revenueGoalStatsProvider);
                          return _buildProgressInfo(revenueStats.achievementRate);
                        },
                      ),
                    ],
                  );
                } else {
                  // 태블릿: 기존 레이아웃
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildInfoColumn('총 목표 수익', '₩ ${NumberFormat('#,###').format(totalTarget)}'),
                      const SizedBox(height: 8),
                      Consumer(
                        builder: (context, ref, child) {
                          final revenueStats = ref.watch(revenueGoalStatsProvider);
                          return _buildInfoColumn(
                            '현재 수익',
                            '₩ ${NumberFormat('#,###').format(revenueStats.totalActual.toInt())}'
                          );
                        },
                      ),
                      const SizedBox(height: 8),
                      Consumer(
                        builder: (context, ref, child) {
                          final revenueStats = ref.watch(revenueGoalStatsProvider);
                          return _buildProgressInfo(revenueStats.achievementRate);
                        },
                      ),
                    ],
                  );
                }
              },
            ),
          ),
          const SizedBox(height: 16),

          // 날짜별 목표 입력 필드들
          Expanded(
            child: ListView.builder(
              itemCount: eventDates.length,
              itemBuilder: (context, index) {
                final date = eventDates[index];
                return _buildOverallDateGoalField(date, overallGoals);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 정보 컬럼 위젯
  Widget _buildInfoColumn(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppColors.secondaryTextColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.primaryTextColor,
          ),
        ),
      ],
    );
  }

  /// 진행률 정보 위젯
  Widget _buildProgressInfo(double progress) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '달성률',
          style: TextStyle(
            fontSize: 12,
            color: AppColors.secondaryTextColor,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: progress,
                backgroundColor: AppColors.secondaryTextColor.withValues(alpha: 0.2),
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primarySeed),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '${(progress * 100).toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.primaryTextColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 전체 목표 날짜별 입력 필드
  Widget _buildOverallDateGoalField(DateTime date, List<RevenueGoal> overallGoals) {
    final dateStr = DateFormat('yyyy-MM-dd').format(date);
    final weekday = _getWeekdayName(date.weekday);

    final existingGoal = overallGoals.firstWhere(
      (g) => g.date == dateStr,
      orElse: () => RevenueGoal(
        eventId: ref.read(currentWorkspaceProvider)!.id,
        sellerId: null, // 전체 목표
        date: dateStr,
        targetAmount: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    if (!_controllers.containsKey('overall')) {
      _controllers['overall'] = {};
    }
    if (!_originalValues.containsKey('overall')) {
      _originalValues['overall'] = {};
    }
    if (!_controllers['overall']!.containsKey(dateStr)) {
      final originalValue = existingGoal.targetAmount > 0 ? NumberFormat('#,###').format(existingGoal.targetAmount.toInt()) : '';
      _controllers['overall']![dateStr] = TextEditingController(text: originalValue);
      _originalValues['overall']![dateStr] = originalValue;
    }

    final controller = _controllers['overall']![dateStr]!;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              '${DateFormat('M/d').format(date)} ($weekday)',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: AppColors.primaryTextColor,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 3,
            child: TextFormField(
              controller: controller,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                _ThousandsSeparatorInputFormatter(),
              ],
              decoration: InputDecoration(
                hintText: '목표 금액',
                suffixText: '원',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppColors.secondaryTextColor.withValues(alpha: 0.3)),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  /// 판매자별 목표 목록 UI
  Widget _buildSellerGoalsList(List<Seller> sellers, List<DateTime> eventDates, List<RevenueGoal> goals) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: sellers.length,
      itemBuilder: (context, index) {
        final seller = sellers[index];
        final sellerId = seller.id?.toString() ?? '';
        final isExpanded = _expandedSellers[sellerId] ?? false;
        final sellerGoals = goals.where((g) => g.sellerId == sellerId).toList();
        final totalTarget = sellerGoals.fold<double>(0, (sum, goal) => sum + goal.targetAmount);

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: Column(
            children: [
              // 판매자 헤더
              ListTile(
                leading: Icon(
                  Icons.person,
                  color: AppColors.primarySeed,
                ),
                title: Text(
                  seller.name,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: AppColors.primaryTextColor,
                  ),
                ),
                subtitle: Text(
                  '총 목표 수익: ₩ ${NumberFormat('#,###').format(totalTarget)}',
                  style: TextStyle(color: AppColors.secondaryTextColor),
                ),
                trailing: IconButton(
                  icon: Icon(
                    isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: AppColors.secondaryTextColor,
                  ),
                  onPressed: () {
                    setState(() {
                      _expandedSellers[sellerId] = !isExpanded;
                    });
                  },
                ),
              ),

              // 확장된 날짜별 목표 입력 필드들
              if (isExpanded) ...[
                const Divider(height: 1),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: eventDates.map((date) {
                      return _buildSellerDateGoalField(seller, date, sellerGoals);
                    }).toList(),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  /// 판매자별 날짜 목표 입력 필드
  Widget _buildSellerDateGoalField(Seller seller, DateTime date, List<RevenueGoal> sellerGoals) {
    final dateStr = DateFormat('yyyy-MM-dd').format(date);
    final weekday = _getWeekdayName(date.weekday);
    final sellerId = seller.id?.toString() ?? '';

    final existingGoal = sellerGoals.firstWhere(
      (g) => g.date == dateStr,
      orElse: () => RevenueGoal(
        eventId: seller.eventId,
        sellerId: sellerId,
        date: dateStr,
        targetAmount: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    if (!_controllers.containsKey(sellerId)) {
      _controllers[sellerId] = {};
    }
    if (!_originalValues.containsKey(sellerId)) {
      _originalValues[sellerId] = {};
    }
    if (!_controllers[sellerId]!.containsKey(dateStr)) {
      final originalValue = existingGoal.targetAmount > 0 ? NumberFormat('#,###').format(existingGoal.targetAmount.toInt()) : '';
      _controllers[sellerId]![dateStr] = TextEditingController(text: originalValue);
      _originalValues[sellerId]![dateStr] = originalValue;
    }

    final controller = _controllers[sellerId]![dateStr]!;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              '${DateFormat('M/d').format(date)} ($weekday)',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: AppColors.primaryTextColor,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 3,
            child: TextFormField(
              controller: controller,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                _ThousandsSeparatorInputFormatter(),
              ],
              decoration: InputDecoration(
                hintText: '목표 금액',
                suffixText: '원',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppColors.secondaryTextColor.withValues(alpha: 0.3)),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  /// 뒤로가기 처리: 미저장 변경 확인
  Future<void> _onBackPressed() async {
    final hasUnsaved = _hasUnsavedChanges();
    if (!hasUnsaved) {
      Navigator.of(context).pop();
      return;
    }

    final confirmed = await UnsavedChangesDialog.show(
      context: context,
    );
    if (confirmed == true && mounted) {
      Navigator.of(context).pop();
    }
  }

  bool _hasUnsavedChanges() {
    for (final sellerEntry in _controllers.entries) {
      final sellerId = sellerEntry.key;
      for (final dateEntry in sellerEntry.value.entries) {
        final dateStr = dateEntry.key;
        final controller = dateEntry.value;
        
        // 현재 값과 원본 값 비교
        final currentValue = controller.text.trim();
        final originalValue = _originalValues[sellerId]?[dateStr] ?? '';
        
        if (currentValue != originalValue) {
          return true;
        }
      }
    }
    return false;
  }

  /// 모든 변경사항 저장 (기존 있으면 Update, 없으면 Add)
  Future<void> _saveAllChanges() async {
    final currentWorkspace = ref.read(currentWorkspaceProvider)!;
    final revenueGoalNotifier = ref.read(revenueGoalNotifierProvider.notifier);

    try {
      // 기존 목표 조회 캐시: (sellerKey/date) -> RevenueGoal
      final existingByKey = <String, RevenueGoal>{};
      final existingGoals = ref.read(revenueGoalNotifierProvider).goals
          .where((g) => g.eventId == currentWorkspace.id)
          .toList();
      for (final g in existingGoals) {
        final sellerKey = g.sellerId ?? 'overall';
        existingByKey['$sellerKey|${g.date}'] = g;
      }

      for (final sellerId in _controllers.keys) {
        for (final dateStr in _controllers[sellerId]!.keys) {
          final controller = _controllers[sellerId]![dateStr]!;
          final amountText = controller.text.replaceAll(',', '');

          if (amountText.isNotEmpty) {
            final amount = double.tryParse(amountText) ?? 0;
            final key = '${sellerId}|$dateStr';
            final existing = existingByKey[key];

            if (amount > 0) {
              if (existing != null) {
                // 업데이트
                final updated = existing.copyWith(
                  targetAmount: amount,
                  updatedAt: DateTime.now(),
                );
                await revenueGoalNotifier.updateGoal(updated);
              } else {
                // 새로 추가
                final goal = RevenueGoal(
                  eventId: currentWorkspace.id,
                  sellerId: sellerId == 'overall' ? null : sellerId,
                  date: dateStr,
                  targetAmount: amount,
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                );
                await revenueGoalNotifier.addGoal(goal);
              }
            } else {
              // 0 또는 공백 → 기존이 있으면 삭제 고려 (요구되지 않아 유지)
            }
          }
        }
      }

      ToastUtils.showSuccess(context, '목표 수익이 저장되었습니다.');
    } catch (e) {
      ToastUtils.showError(context, '저장 중 오류가 발생했습니다: $e');
    }
  }

  /// 요일 이름 가져오기
  String _getWeekdayName(int weekday) {
    const weekdays = ['월', '화', '수', '목', '금', '토', '일'];
    return weekdays[weekday - 1];
  }

  /// 행사 날짜 범위 가져오기
  List<DateTime> _getEventDateRange(dynamic workspace) {
    final startDate = workspace.startDate ?? DateTime.now();
    final endDate = workspace.endDate ?? DateTime.now();

    final dates = <DateTime>[];
    var current = startDate;

    while (current.isBefore(endDate) || current.isAtSameMomentAs(endDate)) {
      dates.add(current);
      current = current.add(const Duration(days: 1));
    }

    return dates;
  }
}

/// 천 단위 구분자 입력 포맷터
class _ThousandsSeparatorInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty) {
      return newValue;
    }

    final number = int.tryParse(newValue.text.replaceAll(',', ''));
    if (number == null) {
      return oldValue;
    }

    final formatted = NumberFormat('#,###').format(number);
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
