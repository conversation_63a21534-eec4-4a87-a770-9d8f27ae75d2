import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import '../models/prepayment.dart';
import '../models/prepayment_sort_order.dart';
import '../repositories/prepayment_repository.dart';
import '../utils/logger_utils.dart';
import '../services/database_service.dart';
import '../services/link_service.dart';
import 'prepayment_state.dart';
import 'settings_provider.dart';
import 'product_provider.dart';
import 'prepayment_virtual_product_provider.dart';
import '../utils/excel_processor.dart';
import 'package:flutter/material.dart';
import 'prepayment_product_link_provider.dart';
import '../models/prepayment_virtual_product.dart';
import '../models/event_workspace.dart';
import 'unified_workspace_provider.dart';
import 'realtime_sync_provider.dart';
import '../services/realtime_sync_service_main.dart';
// import 'data_sync_provider.dart'; // 고유 ID 전략으로 단일 업로드 직접 realtime 서비스 사용
import '../utils/prepayment_id_generator.dart';

/// 선결제 데이터베이스 접근을 위한 Repository Provider입니다.
final prepaymentRepositoryProvider = Provider<PrepaymentRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return PrepaymentRepository(database: databaseService);
});

/// 선결제 상태를 관리하는 Provider입니다.
/// - 선결제 목록, CRUD, 상태 변화, 예외 처리 등 모든 비즈니스 로직 담당
class PrepaymentNotifier extends StateNotifier<PrepaymentState> {
  final Ref ref;
  final bool autoInit;
  bool _isPaused = false;
  StreamSubscription<RealtimeDataChange>? _realtimeSubscription;

  // [신규] 임시 선입금 데이터 리스트
  final List<Prepayment> _tempPrepaymentList = [];

  // 무한 루프 방지를 위한 최근 추가한 선입금 캐시
  final Set<int> _recentlyAddedPrepayments = <int>{};

  PrepaymentNotifier(this.ref, {this.autoInit = true}) : super(PrepaymentState.initialState()) {
    if (autoInit) {
      loadPrepayments();
    }
    _watchCurrentEvent();
    _setupRealtimeSync();
  }

  /// 현재 행사 워크스페이스 변경 감지 및 자동 새로고침
  void _watchCurrentEvent() {
    ref.listen<EventWorkspace?>(currentWorkspaceProvider, (previous, next) {
      if (previous?.id != next?.id) {
        LoggerUtils.logInfo('현재 행사 워크스페이스 변경 감지 - PrepaymentNotifier 새로고침: ${previous?.name} -> ${next?.name}', tag: 'PrepaymentNotifier');
        
        // 행사 전환 시 메모리 완전 클리어
        _clearAllDataForEventTransition();
        
        if (next != null) {
          loadPrepayments();
          _setupRealtimeSync(); // 새 워크스페이스에 대한 실시간 동기화 재설정
        } else {
          // 현재 행사 워크스페이스가 null이 되면 선입금 목록 클리어
          state = state.copyWith(
            prepayments: [],
            filteredPrepayments: [],
            errorMessage: '행사 워크스페이스를 선택해주세요. 왼쪽 상단 메뉴에서 행사 워크스페이스를 선택하거나 생성할 수 있습니다.',
          );
        }
      }
    });
  }

  /// 실시간 동기화 설정
  void _setupRealtimeSync() {
    try {
      // 기존 구독 해제
      _realtimeSubscription?.cancel();
      
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 워크스페이스가 없어 실시간 동기화를 설정할 수 없습니다', tag: 'PrepaymentNotifier');
        return;
      }
      
      // 실시간 동기화 서비스 가져오기
      final realtimeService = ref.read(realtimeSyncServiceProvider);
      
      // 중복 구독 방지: 이미 다른 곳에서 구독이 시작되었다고 가정
      LoggerUtils.logInfo('실시간 데이터 스트림 구독 시작 (중복 구독 방지)', tag: 'PrepaymentNotifier');
      
      // 데이터 변경 리스너 설정
      _realtimeSubscription = realtimeService.dataChanges.listen((change) {
        _handleRealtimeDataChange(change);
      });
      
      LoggerUtils.logInfo('PrepaymentNotifier 실시간 동기화 리스너 설정 완료', tag: 'PrepaymentNotifier');
    } catch (e) {
      LoggerUtils.logError('PrepaymentNotifier 실시간 동기화 설정 실패', tag: 'PrepaymentNotifier', error: e);
    }
  }

  /// 행사 전환 시 메모리 완전 클리어 (메모리 누수 방지)
  void _clearAllDataForEventTransition() {
    try {
      LoggerUtils.logInfo('행사 전환 - 메모리 클리어 시작', tag: 'PrepaymentNotifier');

      // 1. 모든 구독 안전하게 해제
      try {
        _realtimeSubscription?.cancel();
        _realtimeSubscription = null;
      } catch (e) {
        LoggerUtils.logError('실시간 구독 해제 실패', tag: 'PrepaymentNotifier', error: e);
      }

      // 2. 상태 완전 초기화
      state = PrepaymentState.initialState();

      // 3. 내부 변수 클리어
      _tempPrepaymentList.clear();

      LoggerUtils.logInfo('행사 전환 - 메모리 클리어 완료', tag: 'PrepaymentNotifier');
    } catch (e) {
      LoggerUtils.logError('메모리 클리어 중 오류', tag: 'PrepaymentNotifier', error: e);
    }
  }

  /// 실시간 데이터 변경 처리
  void _handleRealtimeDataChange(RealtimeDataChange change) {
    try {
      final currentWorkspace = ref.read(currentWorkspaceProvider);

      // 현재 워크스페이스의 선입금 변경인지 확인
      if (currentWorkspace?.id == change.eventId && change.collectionName == 'prepayments') {
        final prepaymentId = int.tryParse(change.documentId);

        // 자기가 최근에 추가한 선입금은 무시 (무한 루프 방지)
        if (prepaymentId != null && _recentlyAddedPrepayments.contains(prepaymentId)) {
          LoggerUtils.logDebug('최근 추가한 선입금 무시: ID $prepaymentId', tag: 'PrepaymentNotifier');
          return;
        }

        LoggerUtils.logInfo('선입금 실시간 변경 감지: ${change.changeType.name} - ${change.documentId}', tag: 'PrepaymentNotifier');

        // 선입금 목록 즉시 새로고침 (로딩 없이)
        loadPrepayments(showLoading: false);
      }
    } catch (e) {
      LoggerUtils.logError('실시간 데이터 변경 처리 실패', tag: 'PrepaymentNotifier', error: e);
    }
  }

  /// PrepaymentNotifier dispose 처리
  @override
  void dispose() {
    _realtimeSubscription?.cancel(); // 실시간 동기화 구독 해제
    super.dispose();
  }

  /// 레거시 id==0 선입금들을 새로운 고유 ID로 재할당 (최초 한 번 실행)
  Future<void> _migrateZeroIdPrepayments(List<Prepayment> zeroIdList) async {
    if (zeroIdList.isEmpty) return;
    LoggerUtils.logInfo('레거시 ID=0 선입금 마이그레이션 시작: ${zeroIdList.length}개', tag: 'PrepaymentNotifier');
    final repository = ref.read(prepaymentRepositoryProvider);
    final realtimeService = ref.read(realtimeSyncServiceProvider);
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    for (final old in zeroIdList) {
      try {
        final newId = PrepaymentIdGenerator.generate();
        final migrated = old.copyWith(id: newId);
        // 새 레코드 삽입
        await repository.insertPrepayment(migrated);
        // 오래된 0 ID 레코드 삭제
        await repository.deletePrepayment(old.id);
        // 원격 0 문서 삭제 & 새 문서 업로드
        if (currentWorkspace != null) {
          try { await realtimeService.deletePrepayment(currentWorkspace.id, 0); } catch (_) {}
          try { await realtimeService.addPrepayment(currentWorkspace.id, migrated); } catch (e) {
            LoggerUtils.logError('마이그레이션 신규 업로드 실패: ${migrated.id}', tag: 'PrepaymentNotifier', error: e);
          }
        }
      } catch (e) {
        LoggerUtils.logError('ID=0 마이그레이션 실패', tag: 'PrepaymentNotifier', error: e);
      }
    }
    await loadPrepayments(showLoading: false);
    LoggerUtils.logInfo('레거시 ID=0 선입금 마이그레이션 완료', tag: 'PrepaymentNotifier');
  }

  /// [신규] 임시 선입금 데이터 추가
  Future<void> addTempPrepaymentData(Prepayment prepayment) async {
    _tempPrepaymentList.add(prepayment);
  }

  /// [신규] 임시 선입금 데이터 전체 삭제
  Future<void> clearTempPrepaymentData() async {
    _tempPrepaymentList.clear();
  }

  /// [신규] 임시 선입금 데이터 전체 조회
  List<Prepayment> getTempPrepaymentList() {
    return List.unmodifiable(_tempPrepaymentList);
  }

  void pause() {
    if (!_isPaused) {
      _isPaused = true;
      LoggerUtils.logDebug('PrepaymentNotifier paused', tag: 'PrepaymentNotifier');
    }
  }

  void resume([Duration? duration]) {
    if (_isPaused) {
      _isPaused = false;
      LoggerUtils.logDebug('PrepaymentNotifier resumed', tag: 'PrepaymentNotifier');
      loadPrepayments(showLoading: false);
    }
  }

  Future<void> loadPrepayments({bool showLoading = true}) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('loadPrepayments', tag: 'PrepaymentNotifier');
    if (showLoading) {
      state = state.copyWith(isLoading: true);
    }
    int retry = 0;
    while (retry < 3) {
      try {
        // 현재 선택된 행사 워크스페이스 확인
        EventWorkspace? currentWorkspace = ref.read(currentWorkspaceProvider);

        if (currentWorkspace == null) {
          LoggerUtils.logWarning('현재 선택된 워크스페이스가 없습니다', tag: 'PrepaymentNotifier');
          state = state.copyWith(
            prepayments: [],
            filteredPrepayments: [],
            isLoading: false,
            errorMessage: '워크스페이스를 선택해주세요. 왼쪽 상단 메뉴에서 워크스페이스를 선택하거나 생성할 수 있습니다.',
          );
          return;
        }

        final repository = ref.read(prepaymentRepositoryProvider);
        final prepayments = await repository.getPrepaymentsByEventId(currentWorkspace.id);
        state = state.copyWith(
          prepayments: prepayments,
          filteredPrepayments: _applyFiltersAndSort(prepayments, state),
          isLoading: false,
          errorMessage: null,
        );
        // ID 0 레거시 데이터 마이그레이션
        if (prepayments.any((p) => p.id == 0)) {
          _migrateZeroIdPrepayments(prepayments.where((p) => p.id == 0).toList());
        }
        return;
      } catch (e) {
        final msg = e.toString();
        if (msg.contains('no such table') || msg.contains('database is not open')) {
          retry++;
          await Future.delayed(const Duration(milliseconds: 500));
          continue;
        } else {
          state = state.copyWith(
            isLoading: false,
            errorMessage: msg,
          );
          return;
        }
      }
    }
    state = state.copyWith(isLoading: false, errorMessage: '데이터베이스 초기화 중입니다. 잠시 후 다시 시도해주세요.');
  }

  Future<void> addPrepayment(Prepayment prepayment) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('addPrepayment', tag: 'PrepaymentNotifier');

    try {
      // 현재 선택된 워크스페이스 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 선택된 워크스페이스가 없습니다', tag: 'PrepaymentNotifier');
        state = state.copyWith(errorMessage: '워크스페이스를 선택해주세요');
        return;
      }

      // 고유 ID 미할당(id==0) 시 즉시 생성 (DB/Firestore 모두 동일 사용)
      if (prepayment.id == 0) {
        try {
          final newId = PrepaymentIdGenerator.generate();
          prepayment = prepayment.copyWith(id: newId);
        } catch (e) {
          LoggerUtils.logError('ID 생성 실패, 타임스탬프 fallback', tag: 'PrepaymentNotifier', error: e);
          prepayment = prepayment.copyWith(id: DateTime.now().microsecondsSinceEpoch);
        }
      }

      // 선입금에 현재 워크스페이스 ID 설정 (id는 유지)
      final prepaymentWithEventId = prepayment.copyWith(eventId: currentWorkspace.id);

      // 1. 로컬 DB에 저장
      final repository = ref.read(prepaymentRepositoryProvider);
  await repository.insertPrepayment(prepaymentWithEventId); // 결과 ID 무시 (이미 고유 ID 적용)
      // insert가 -1 (오프라인) 이거나 0(무시) 이어도 우리는 이미 고유 ID를 가지고 있다.
      final savedPrepayment = prepaymentWithEventId;

      // 최근 추가한 선입금으로 캐시 (무한 루프 방지용)
      _recentlyAddedPrepayments.add(savedPrepayment.id);
      // 5초 후 캐시에서 제거
      Future.delayed(const Duration(seconds: 5), () {
        _recentlyAddedPrepayments.remove(savedPrepayment.id);
      });

      // 2. Firebase에 즉시 업로드 (실시간 동기화를 위해)
      try {
        final realtimeService = ref.read(realtimeSyncServiceProvider);
        await realtimeService.addPrepayment(savedPrepayment.eventId, savedPrepayment);
        LoggerUtils.logInfo('선입금 Firebase 업로드 성공: ID ${savedPrepayment.id}', tag: 'PrepaymentNotifier');
      } catch (e) {
        LoggerUtils.logError('선입금 Firebase 업로드 실패 (로컬 저장은 성공): ID ${savedPrepayment.id}', tag: 'PrepaymentNotifier', error: e);
      }

      // 상태 업데이트 복원 - 선입금 등록 후 즉시 목록 갱신
      await loadPrepayments(showLoading: false);

      // 신규 선입금의 상품을 가상 상품 수량에 반영
      await _addVirtualProductQuantitiesForNewPrepayment(savedPrepayment);

    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('addPrepayment', tag: 'PrepaymentNotifier');
    }
  }

  Future<void> updatePrepayment(Prepayment prepayment) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('updatePrepayment', tag: 'PrepaymentNotifier');

    try {
      // 수정 전에 기존 선입금 정보를 가져와서 가상 상품 수량 차감에 사용
      final existingPrepayment = state.prepayments.firstWhere(
        (p) => p.id == prepayment.id,
        orElse: () => throw Exception('수정할 선입금을 찾을 수 없습니다.'),
      );

      final repository = ref.read(prepaymentRepositoryProvider);
      await repository.updatePrepayment(prepayment);
      await loadPrepayments(showLoading: false);
      
      // 기존 상품 수량을 차감하고 새로운 상품 수량을 추가
      await _updateVirtualProductQuantities(existingPrepayment, prepayment);
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('updatePrepayment', tag: 'PrepaymentNotifier');
    }
  }

  Future<void> deletePrepayment(int id) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('deletePrepayment', tag: 'PrepaymentNotifier');

    try {
      // 삭제 전에 선입금 정보를 가져와서 가상 상품 수량 차감에 사용
      final prepaymentToDelete = state.prepayments.firstWhere(
        (p) => p.id == id,
        orElse: () => throw Exception('삭제할 선입금을 찾을 수 없습니다.'),
      );

      final repository = ref.read(prepaymentRepositoryProvider);
      await repository.deletePrepayment(id);
      await loadPrepayments(showLoading: false);
      
      // 삭제된 선입금의 상품 수량을 가상 상품 데이터에서 차감
      await _subtractVirtualProductQuantities(prepaymentToDelete);

      // Firebase에서도 삭제 (실패해도 로컬은 삭제됨)
      try {
        final realtimeService = ref.read(realtimeSyncServiceProvider);
        final currentWorkspace = ref.read(currentWorkspaceProvider);
        if (currentWorkspace != null) {
          await realtimeService.deletePrepayment(currentWorkspace.id, id);
        }
      } catch (e) {
        LoggerUtils.logError('Firebase 선입금 삭제 실패(로컬 성공): $id', tag: 'PrepaymentNotifier', error: e);
      }
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('deletePrepayment', tag: 'PrepaymentNotifier');
    }
  }

  Future<void> updateReceiveStatus(BuildContext context, int id, bool isReceived) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('updateReceiveStatus', tag: 'PrepaymentNotifier');

    try {
      final repository = ref.read(prepaymentRepositoryProvider);
      final databaseService = ref.read(databaseServiceProvider);
      final linkService = LinkService(databaseService: databaseService);
      // 수령 상태 변경 전에 현재 선입금 정보 가져오기
      final currentPrepayment = await repository.getPrepaymentById(id);
      if (currentPrepayment == null) {
        LoggerUtils.logError('선입금을 찾을 수 없음: ID $id', tag: 'PrepaymentNotifier');
        return;
      }
      await repository.updateReceiveStatus(id, isReceived);
      final linkPrepaymentToInventory = ref.read(linkPrepaymentToInventoryProvider);
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) return;

      if (linkPrepaymentToInventory) {
        if (isReceived) {
          // 수령 완료로 변경된 경우: 연동 및 재고 차감 (LinkService만 호출)
          await linkService.linkProductsWithPrepayments(
            prepaymentDataList: [ExcelProcessor.fromPrepayment(currentPrepayment)],
            context: context,
            eventId: currentWorkspace.id,
          );
          // 연동된 상품의 재고 차감 - decreaseStock 메서드 사용으로 중복 Firebase 업로드 방지
          final linkRepo = ref.read(prepaymentProductLinkRepositoryProvider);
          final productRepo = ref.read(productRepositoryProvider);
          final virtualProductState = ref.read(prepaymentVirtualProductNotifierProvider);

          for (final purchased in currentPrepayment.purchasedProducts) {
            final vp = virtualProductState.virtualProducts.firstWhere(
              (v) => v.name == purchased.name,
              orElse: () => PrepaymentVirtualProduct(id: -1, name: '', price: 0, quantity: 0, createdAt: DateTime.now(), eventId: 1),
            );
            if (vp.id == -1) continue;
            final links = await linkRepo.getLinksByVirtualProductId(vp.id, currentWorkspace.id);
            for (final link in links) {
              // 재고 차감은 decreaseStock 메서드 사용
              await productRepo.decreaseStock(link.productId, purchased.quantity);
            }
          }
        } else {
          // 미수령으로 되돌린 경우: 연동 해제 없이 재고만 복구
          final linkRepo = ref.read(prepaymentProductLinkRepositoryProvider);
          final productRepo = ref.read(productRepositoryProvider);
          final virtualProductState = ref.read(prepaymentVirtualProductNotifierProvider);
          final currentWorkspace2 = ref.read(currentWorkspaceProvider);
          if (currentWorkspace2 == null) return;

          for (final purchased in currentPrepayment.purchasedProducts) {
            final vp = virtualProductState.virtualProducts.firstWhere(
              (v) => v.name == purchased.name,
              orElse: () => PrepaymentVirtualProduct(id: -1, name: '', price: 0, quantity: 0, createdAt: DateTime.now(), eventId: 1),
            );
            if (vp.id == -1) continue;
            final links = await linkRepo.getLinksByVirtualProductId(vp.id, currentWorkspace2.id);
            for (final link in links) {
              // 재고 복구는 increaseStock 메서드 사용
              await productRepo.increaseStock(link.productId, purchased.quantity);
            }
          }
        }
      }
      await loadPrepayments(showLoading: false);
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('updateReceiveStatus', tag: 'PrepaymentNotifier');
    }
  }

  /// 가상 상품 데이터를 갱신하는 메서드
  // (이전) _refreshVirtualProducts 메서드는 신규 등록 시 직접 addVirtualProduct 로직으로 대체되어 제거됨

  /// 삭제된 선입금의 상품 수량을 가상 상품 데이터에서 차감하는 메서드
  Future<void> _subtractVirtualProductQuantities(Prepayment deletedPrepayment) async {
    try {
      final virtualProductNotifier = ref.read(prepaymentVirtualProductNotifierProvider.notifier);
      
      // 삭제된 선입금의 각 상품 수량을 차감
      for (final product in deletedPrepayment.purchasedProducts) {
        virtualProductNotifier.subtractVirtualProductQuantity(product.name, product.quantity);
      }
    } catch (e) {
      LoggerUtils.logError(
        '가상 상품 데이터 수량 차감 중 오류 발생: $e',
        tag: 'PrepaymentNotifier',
      );
    }
  }

  /// 선입금 수정 시 가상 상품 데이터 수량을 업데이트하는 메서드
  Future<void> _updateVirtualProductQuantities(Prepayment oldPrepayment, Prepayment newPrepayment) async {
    try {
      final virtualProductNotifier = ref.read(prepaymentVirtualProductNotifierProvider.notifier);
      
      // 기존 상품 수량을 차감
      for (final product in oldPrepayment.purchasedProducts) {
        virtualProductNotifier.subtractVirtualProductQuantity(product.name, product.quantity);
      }
      
      // 새로운 상품 수량을 추가
      for (final product in newPrepayment.purchasedProducts) {
        virtualProductNotifier.addVirtualProduct(product.name, product.quantity);
      }
    } catch (e) {
      LoggerUtils.logError(
        '가상 상품 데이터 수량 업데이트 중 오류 발생: $e',
        tag: 'PrepaymentNotifier',
      );
    }
  }

  /// 신규 선입금 등록 시 가상 상품 수량을 추가하는 메서드
  Future<void> _addVirtualProductQuantitiesForNewPrepayment(Prepayment newPrepayment) async {
    try {
      final virtualProductNotifier = ref.read(prepaymentVirtualProductNotifierProvider.notifier);
      if (newPrepayment.purchasedProducts.isEmpty) {
        LoggerUtils.logDebug('신규 선입금에 연결된 상품이 없어 가상 상품 추가 스킵: ID ${newPrepayment.id}', tag: 'PrepaymentNotifier');
        return;
      }
      for (final product in newPrepayment.purchasedProducts) {
        if (product.name.trim().isEmpty || product.quantity <= 0) continue;
        virtualProductNotifier.addVirtualProduct(product.name.trim(), product.quantity);
      }
      LoggerUtils.logInfo('신규 선입금(ID: ${newPrepayment.id}) 가상 상품 수량 반영 완료 (${newPrepayment.purchasedProducts.length}개)', tag: 'PrepaymentNotifier');
    } catch (e) {
      LoggerUtils.logError('신규 선입금 가상 상품 수량 반영 중 오류: $e', tag: 'PrepaymentNotifier');
    }
  }

  /// ID로 특정 선결제 정보를 조회합니다.
  Future<Prepayment?> getPrepaymentById(int id) async {
    final repository = ref.read(prepaymentRepositoryProvider);
    return await repository.getPrepaymentById(id);
  }

  /// 수령 상태를 토글합니다.
  Future<void> toggleReceiveStatus(int id) async {
    final repository = ref.read(prepaymentRepositoryProvider);
    final prepayment = await repository.getPrepaymentById(id);
    if (prepayment != null) {
      await repository.updateReceiveStatus(id, !prepayment.isReceived);
      await loadPrepayments(showLoading: false);
    }
  }

  Future<void> setSortOrder(PrepaymentSortOrder sortOrder) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('setSortOrder', tag: 'PrepaymentNotifier');

    try {
      final repository = ref.read(prepaymentRepositoryProvider);
      final prepayments = await repository.getPrepaymentsSorted(sortOrder);
      state = state.copyWith(
        prepayments: prepayments,
        filteredPrepayments: _applyFiltersAndSort(prepayments, state),
        sortOrder: sortOrder,
        errorMessage: null,
      );
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('setSortOrder', tag: 'PrepaymentNotifier');
    }
  }

  Future<void> setReceivedFilter(bool showReceived) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('setReceivedFilter', tag: 'PrepaymentNotifier');

    try {
      state = state.copyWith(showReceivedOnly: showReceived);
      state = state.copyWith(
        filteredPrepayments: _applyFiltersAndSort(state.prepayments, state),
      );
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('setReceivedFilter', tag: 'PrepaymentNotifier');
    }
  }

  Future<void> search(String query) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('search', tag: 'PrepaymentNotifier');

    try {
      state = state.copyWith(searchQuery: query);
      state = state.copyWith(
        filteredPrepayments: _applyFiltersAndSort(state.prepayments, state),
      );
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('search', tag: 'PrepaymentNotifier');
    }
  }

  Future<void> searchPrepayments(String query) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('searchPrepayments', tag: 'PrepaymentNotifier');

    try {
      final repository = ref.read(prepaymentRepositoryProvider);
      final prepayments = await repository.searchPrepayments(query);
      state = state.copyWith(
        filteredPrepayments: prepayments,
        searchQuery: query,
        errorMessage: null,
      );
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('searchPrepayments', tag: 'PrepaymentNotifier');
    }
  }

  Future<void> filterByDayOfWeek(int dayOfWeek) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('filterByDayOfWeek', tag: 'PrepaymentNotifier');

    try {
      final allPrepayments = state.prepayments;
      List<Prepayment> filtered;
      if (dayOfWeek == 0) {
        filtered = allPrepayments;
      } else {
        filtered = allPrepayments.where((p) {
          // registrationActualDayOfWeek 체크
          bool match = false;
          if (dayOfWeek == 8) {
            // '없음' 처리
            if (p.registrationActualDayOfWeek == 0 || p.registrationActualDayOfWeek == 8) {
              match = true;
            }
            if (p.pickupDays.any((d) => d == Prepayment.noDayOfWeek || Prepayment.availableDaysOfWeek.indexOf(d) == 7)) {
              match = true;
            }
          } else {
            if (p.registrationActualDayOfWeek == dayOfWeek) {
              match = true;
            }
            if (p.pickupDays.any((d) => Prepayment.availableDaysOfWeek.indexOf(d) == (dayOfWeek - 1))) {
              match = true;
            }
          }
          return match;
        }).toList();
      }
      state = state.copyWith(filteredPrepayments: filtered, errorMessage: null);
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('filterByDayOfWeek', tag: 'PrepaymentNotifier');
    }
  }

  Future<void> loadPrepaymentsSorted(PrepaymentSortOrder sortOrder) async {
    if (_isPaused) return;
    LoggerUtils.methodStart('loadPrepaymentsSorted', tag: 'PrepaymentNotifier');

    try {
      // 현재 선택된 행사 워크스페이스 확인 (최대 3초 대기)
      EventWorkspace? currentWorkspace = ref.read(currentWorkspaceProvider);

      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 선택된 워크스페이스가 없습니다', tag: 'PrepaymentNotifier');
        state = state.copyWith(
          prepayments: [],
          filteredPrepayments: [],
          sortOrder: sortOrder,
          errorMessage: '워크스페이스를 선택해주세요',
        );
        return;
      }

      final repository = ref.read(prepaymentRepositoryProvider);
      final prepayments = await repository.getPrepaymentsSorted(sortOrder, eventId: currentWorkspace.id);
      state = state.copyWith(
        prepayments: prepayments,
        filteredPrepayments: prepayments,
        sortOrder: sortOrder,
        errorMessage: null,
      );
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    } finally {
      LoggerUtils.methodEnd('loadPrepaymentsSorted', tag: 'PrepaymentNotifier');
    }
  }

  /// 필터와 정렬을 적용하여 필터링된 목록을 반환합니다.
  List<Prepayment> _applyFiltersAndSort(List<Prepayment> prepayments, PrepaymentState currentState) {
    List<Prepayment> filtered = List.from(prepayments);

    // 검색 필터 적용
    if (currentState.searchQuery.isNotEmpty) {
      final query = currentState.searchQuery.toLowerCase();
      filtered = filtered.where((prepayment) {
        return prepayment.buyerName.toLowerCase().contains(query) ||
               prepayment.amount.toString().contains(query);
      }).toList();
    }

    // 수령 상태 필터 적용 (기존 showReceivedOnly 유지하면서 새로운 filter도 지원)
    if (currentState.showReceivedOnly) {
      filtered = filtered.where((prepayment) => prepayment.isReceived).toList();
    } else {
      // 새로운 필터 시스템 적용
      switch (currentState.filter) {
        case PrepaymentFilter.received:
          filtered = filtered.where((prepayment) => prepayment.isReceived).toList();
          break;
        case PrepaymentFilter.unreceived:
          filtered = filtered.where((prepayment) => !prepayment.isReceived).toList();
          break;
        case PrepaymentFilter.all:
          // 모든 항목 표시 (필터링 없음)
          break;
      }
    }

    // 정렬 적용
    switch (currentState.sortOrder) {
      case PrepaymentSortOrder.buyerNameAsc:
        filtered.sort((a, b) => a.buyerName.compareTo(b.buyerName));
        break;
      case PrepaymentSortOrder.buyerNameDesc:
        filtered.sort((a, b) => b.buyerName.compareTo(a.buyerName));
        break;
      case PrepaymentSortOrder.amountAsc:
        filtered.sort((a, b) => a.amount.compareTo(b.amount));
        break;
      case PrepaymentSortOrder.amountDesc:
        filtered.sort((a, b) => b.amount.compareTo(a.amount));
        break;
      case PrepaymentSortOrder.writtenDateAsc:
        filtered.sort((a, b) => a.registrationDate.compareTo(b.registrationDate));
        break;
      case PrepaymentSortOrder.writtenDateDesc:
        filtered.sort((a, b) => b.registrationDate.compareTo(a.registrationDate));
        break;
    }

    return filtered;
  }

  /// 필터를 설정합니다.
  void setFilter(PrepaymentFilter filter) {
    if (_isPaused) return;

    final currentState = state;
    final filteredPrepayments = _applyFiltersAndSort(currentState.prepayments, currentState.copyWith(filter: filter));

    state = state.copyWith(
      filter: filter,
      filteredPrepayments: filteredPrepayments,
    );
  }

  /// 에러 상태를 초기화합니다.
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  /// 로딩 상태를 설정합니다.
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }
}

/// 선결제 상태를 관리하는 Provider입니다.
final prepaymentNotifierProvider = StateNotifierProvider<PrepaymentNotifier, PrepaymentState>((ref) {
  return PrepaymentNotifier(ref, autoInit: false); // 자동 초기화 비활성화
});

/// 선결제 정렬 순서를 관리하는 Provider입니다.
final prepaymentSortOrderProvider = StateProvider<PrepaymentSortOrder>((ref) {
  return PrepaymentSortOrder.buyerNameAsc;
});

/// 선결제 요일 필터를 관리하는 Provider입니다.
final prepaymentDayOfWeekFilterProvider = StateProvider<int>((ref) {
  return 0; // 0: 전체, 1-7: 요일, 8: 없음
});

/// 선결제 에러 메시지 Provider
final prepaymentErrorMessageProvider = StateProvider<String?>((ref) {
  final state = ref.watch(prepaymentNotifierProvider);
  return state.errorMessage;
});

/// 선결제 검색어 Provider
final prepaymentSearchQueryProvider = StateProvider<String>((ref) {
  final state = ref.watch(prepaymentNotifierProvider);
  return state.searchQuery;
});

/// Prepayment 데이터 동기화 관리자
class PrepaymentDataSyncManager {
  static const String _tag = 'PrepaymentDataSyncManager';
  
  /// 모든 Prepayment 관련 Provider를 일관되게 갱신
  static Future<void> syncAllPrepaymentData(WidgetRef ref) async {
    LoggerUtils.logInfo('Prepayment 데이터 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Prepayment Notifier 갱신
      await ref.read(prepaymentNotifierProvider.notifier).loadPrepayments();
      
      LoggerUtils.logInfo('Prepayment 데이터 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Prepayment 데이터 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Prepayment 추가 후 동기화
  static Future<void> syncAfterAddPrepayment(WidgetRef ref, Prepayment prepayment) async {
    LoggerUtils.logInfo('Prepayment 추가 후 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Prepayment Notifier 추가
      await ref.read(prepaymentNotifierProvider.notifier).addPrepayment(prepayment);
      
      LoggerUtils.logInfo('Prepayment 추가 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Prepayment 추가 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Prepayment 수정 후 동기화
  static Future<void> syncAfterUpdatePrepayment(WidgetRef ref, Prepayment prepayment) async {
    LoggerUtils.logInfo('Prepayment 수정 후 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Prepayment Notifier 수정
      await ref.read(prepaymentNotifierProvider.notifier).updatePrepayment(prepayment);
      
      LoggerUtils.logInfo('Prepayment 수정 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Prepayment 수정 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Prepayment 삭제 후 동기화
  static Future<void> syncAfterDeletePrepayment(WidgetRef ref, int id) async {
    LoggerUtils.logInfo('Prepayment 삭제 후 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Prepayment Notifier 삭제
      await ref.read(prepaymentNotifierProvider.notifier).deletePrepayment(id);
      
      LoggerUtils.logInfo('Prepayment 삭제 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Prepayment 삭제 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
}

/// 실제 등록된 선입금 데이터의 요일만 집계하여 제공하는 Provider
final prepaymentAvailableDaysOfWeekProvider = Provider<Set<int>>((ref) {
  final prepayments = ref.watch(prepaymentNotifierProvider).prepayments;
  final Set<int> days = {};
  for (final p in prepayments) {
    // registrationActualDayOfWeek가 1~7, 8('없음')이면 추가
    if (p.registrationActualDayOfWeek >= 1 && p.registrationActualDayOfWeek <= 7) {
      days.add(p.registrationActualDayOfWeek);
    } else if (p.registrationActualDayOfWeek == 0 || p.registrationActualDayOfWeek == 8) {
      days.add(8); // '없음'
    }
    // pickupDays에도 요일명이 있으면 추가
    for (final dayName in p.pickupDays) {
      final idx = Prepayment.availableDaysOfWeek.indexOf(dayName);
      if (idx >= 0 && idx < 7) {
        days.add(idx + 1); // 1~7
      } else if (dayName == Prepayment.noDayOfWeek || idx == 7) {
        days.add(8); // '없음'
      }
    }
  }
  return days;
});
