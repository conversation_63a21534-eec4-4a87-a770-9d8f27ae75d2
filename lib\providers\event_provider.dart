import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';

import '../models/event.dart';
import '../models/event_state.dart';
import '../models/event_sort_option.dart';
import '../models/seller.dart';
import '../repositories/event_repository.dart';
import '../utils/logger_utils.dart';
import 'unified_workspace_provider.dart';
import '../utils/event_workspace_utils.dart';
import 'nickname_provider.dart';
import 'seller_provider.dart';
import 'realtime_sync_provider.dart';
import '../services/realtime_sync_service_main.dart';
import 'data_sync_provider.dart';

/// Event 상태를 관리하는 StateNotifier입니다.
/// 
/// 주요 기능:
/// - 행사 목록 관리
/// - CRUD 작업
/// - 검색 및 필터링
/// - 현재 선택된 행사 관리
class EventNotifier extends StateNotifier<EventState> {
  static const String _tag = 'EventProvider';

  final EventRepository _repository;
  final Ref _ref;
  StreamSubscription<RealtimeDataChange>? _realtimeSubscription;

  // 무한 루프 방지를 위한 최근 추가한 행사 캐시
  final Set<int> _recentlyAddedEvents = <int>{};

  EventNotifier(this._repository, this._ref) : super(const EventState()) {
    _initializeEvents();
    _setupRealtimeSync();
  }

  /// 초기 행사 데이터를 로드합니다.
  Future<void> _initializeEvents() async {
    try {
      LoggerUtils.methodStart('_initializeEvents', tag: _tag);
      
      // 행사 목록 로드
      await loadEvents();
      
      // 현재 선택된 행사 설정
      await _setInitialCurrentEvent();
      
      LoggerUtils.methodEnd('_initializeEvents', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '초기 행사 데이터 로드 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );
      state = state.copyWith(
        isLoading: false,
        errorMessage: '행사 데이터를 불러오는데 실패했습니다: ${e.toString()}',
      );
    }
  }

  /// 초기 현재 행사를 설정합니다.
  Future<void> _setInitialCurrentEvent() async {
    try {
      final currentWorkspace = _ref.read(currentWorkspaceProvider);
      
      // 이미 현재 워크스페이스가 설정되어 있으면 덮어쓰지 않음 (온보딩 완료 후 보존)
      if (currentWorkspace != null) {
        LoggerUtils.logInfo('현재 워크스페이스가 이미 설정되어 있음: ${currentWorkspace.name} - 초기 설정 생략', tag: _tag);
        return;
      }

      // 마지막 사용한 행사로 설정 (SharedPreferences에서 복원)
      // 이 로직은 이미 unified_workspace_provider에서 처리됨
      LoggerUtils.logInfo('마지막 사용한 행사는 워크스페이스 프로바이더에서 복원됨', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('초기 현재 행사 설정 실패', tag: _tag, error: e);
    }
  }

  /// 실시간 동기화 설정
  void _setupRealtimeSync() {
    try {
      // 기존 구독 해제
      _realtimeSubscription?.cancel();

      // 실시간 동기화 서비스 가져오기
      final realtimeService = _ref.read(realtimeSyncServiceProvider);

      // 실시간 동기화가 활성화된 경우에만 구독
      if (realtimeService.realtimeSyncEnabled) {
        LoggerUtils.logInfo('행사 실시간 데이터 스트림 구독 시작', tag: _tag);

        // 데이터 변경 리스너 설정
        _realtimeSubscription = realtimeService.dataChanges.listen((change) {
          _handleRealtimeDataChange(change);
        });

        LoggerUtils.logInfo('EventNotifier 실시간 동기화 리스너 설정 완료', tag: _tag);
      } else {
        LoggerUtils.logInfo('실시간 동기화가 비활성화되어 EventNotifier 구독을 건너뜁니다', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('EventNotifier 실시간 동기화 설정 실패', tag: _tag, error: e);
    }
  }

  /// 실시간 데이터 변경 처리 - 개별 변경사항만 처리 (Local-First)
  void _handleRealtimeDataChange(RealtimeDataChange change) {
    try {
      // 행사(events) 컬렉션의 변경인지 확인
      if (change.collectionName == 'events') {
        LoggerUtils.logInfo('행사 실시간 변경 감지: ${change.changeType.name} - ${change.documentId}', tag: _tag);
        
        // 개별 변경사항만 처리 (전체 재로드 없이)
        _processSingleEventChange(change);
      }
    } catch (e) {
      LoggerUtils.logError('실시간 데이터 변경 처리 실패', tag: _tag, error: e);
    }
  }

  /// 개별 행사 변경사항 처리
  Future<void> _processSingleEventChange(RealtimeDataChange change) async {
    try {
      final eventId = int.tryParse(change.documentId);
      if (eventId == null) return;

      switch (change.changeType) {
        case RealtimeChangeType.added:
        case RealtimeChangeType.modified:
          // 자기가 최근에 추가한 행사는 무시 (무한 루프 방지)
          if (_recentlyAddedEvents.contains(eventId)) {
            LoggerUtils.logDebug('최근 추가한 행사 무시: ID $eventId', tag: _tag);
            return;
          }

          if (change.data != null) {
            final eventData = Map<String, dynamic>.from(change.data!);
            eventData['id'] = eventId;
            final event = Event.fromFirebaseMap(eventData);
            await _syncEventToLocalSafe(event);
            await _refreshEventState();
          }
          break;

        case RealtimeChangeType.removed:
          await _removeEventFromLocalSafe(eventId);
          await _refreshEventState();
          break;
      }
    } catch (e) {
      LoggerUtils.logError('개별 행사 변경 처리 실패: ${change.documentId}', tag: _tag, error: e);
    }
  }

  /// 행사를 로컬에 안전하게 동기화
  Future<void> _syncEventToLocalSafe(Event event) async {
    try {
      // event.id가 null이 아닌지 확인
      if (event.id == null) {
        LoggerUtils.logWarning('행사 ID가 null임 - 동기화 스킵', tag: _tag);
        return;
      }
      
      // 기존 행사 확인
      final existingEvent = await _repository.getEventById(event.id!);
      
      if (existingEvent != null) {
        // 업데이트
        await _repository.updateEvent(event);
        LoggerUtils.logDebug('행사 로컬 업데이트 완료: ID ${event.id}', tag: _tag);
      } else {
        // 새로 추가
        await _repository.insertEvent(event);
        LoggerUtils.logDebug('행사 로컬 추가 완료: ID ${event.id}', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('행사 로컬 동기화 실패: ID ${event.id}', tag: _tag, error: e);
    }
  }

  /// 행사를 로컬에서 안전하게 제거 (실시간 동기화용 - Firebase 재삭제 방지)
  Future<void> _removeEventFromLocalSafe(int eventId) async {
    try {
      // 실시간 동기화로 인한 삭제이므로 로컬 DB에서만 삭제 (Firebase 재삭제 방지)
      // EventRepository.deleteEvent()를 호출하지 않고 직접 로컬 DB 삭제만 수행
      LoggerUtils.logDebug('행사 로컬 삭제 시작 (실시간 동기화): ID $eventId', tag: _tag);

      // 단순히 상태에서만 제거 (이미 Firebase에서 삭제되었으므로)
      // 실제 DB 삭제는 이미 다른 디바이스에서 처리되었을 가능성이 높음
      LoggerUtils.logDebug('행사 로컬 삭제 완료 (실시간 동기화): ID $eventId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('행사 로컬 삭제 실패 (실시간 동기화): ID $eventId', tag: _tag, error: e);
    }
  }

  /// 행사 상태 새로고침 (로컬 DB에서 다시 로드) - 무한 루프 방지
  Future<void> _refreshEventState() async {
    try {
      // 이미 로딩 중이면 건너뛰기 (무한 루프 방지)
      if (state.isLoading) {
        LoggerUtils.logDebug('이미 로딩 중이므로 상태 새로고침 생략', tag: _tag);
        return;
      }
      
      await loadEvents(showLoading: false);
    } catch (e) {
      LoggerUtils.logError('행사 상태 새로고침 실패', tag: _tag, error: e);
    }
  }

  /// EventNotifier dispose 처리
  @override
  void dispose() {
    _realtimeSubscription?.cancel(); // 실시간 동기화 구독 해제
    super.dispose();
  }

  /// 행사 목록을 로드합니다.
  Future<void> loadEvents({
    bool showLoading = true,
    EventFilter? filter,
  }) async {
    try {
      LoggerUtils.methodStart('loadEvents', tag: _tag);
      
      if (showLoading) {
        state = state.copyWith(isLoading: true, errorMessage: null);
      } else {
        state = state.copyWith(isRefreshing: true, errorMessage: null);
      }
      
      final filterToUse = filter ?? state.currentFilter;
      final events = await _repository.getAllEvents(filter: filterToUse);
      
      state = state.copyWith(
        events: events,
        isLoading: false,
        isRefreshing: false,
        currentFilter: filterToUse,
        lastUpdated: DateTime.now(),
        totalCount: events.length,
        filteredCount: events.length,
        errorMessage: null,
      );

      // 행사 목록 로드 후 현재 행사 초기화
      await _setInitialCurrentEvent();

      LoggerUtils.logInfo('행사 ${events.length}개 로드 완료', tag: _tag);
      LoggerUtils.methodEnd('loadEvents', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 목록 로드 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );
      
      state = state.copyWith(
        isLoading: false,
        isRefreshing: false,
        errorMessage: '행사 목록을 불러오는데 실패했습니다: ${e.toString()}',
      );
    }
  }

  /// 새로운 행사를 추가합니다.
  Future<Event?> addEvent(Event event) async {
    try {
      LoggerUtils.methodStart('addEvent', tag: _tag, data: {'name': event.name});

      state = state.copyWith(isLoading: true, errorMessage: null);

      final addedEvent = await _repository.insertEvent(event);

      // 상태 업데이트
      final updatedEvents = [...state.events, addedEvent];
      state = state.copyWith(
        events: updatedEvents,
        isLoading: false,
        lastUpdated: DateTime.now(),
        totalCount: updatedEvents.length,
        filteredCount: updatedEvents.length,
      );

      // 새로 생성된 행사에 닉네임 기반 판매자 자동 생성
      await _createNicknameBasedSeller(addedEvent);

      // 최근 추가한 행사로 캐시 (무한 루프 방지용)
      if (addedEvent.id != null) {
        _recentlyAddedEvents.add(addedEvent.id!);
        // 5초 후 캐시에서 제거 - 안전하게 Future.delayed 사용
        Future.delayed(const Duration(seconds: 5), () {
          _recentlyAddedEvents.remove(addedEvent.id!);
        });
      }

      // 실시간 동기화: 새 행사를 Firebase에 즉시 업로드
      try {
        final dataSyncService = _ref.read(dataSyncServiceProvider);
        await dataSyncService.uploadSingleEvent(addedEvent);
        LoggerUtils.logInfo('새 행사 실시간 동기화 업로드 완료: ${addedEvent.name}', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('새 행사 실시간 동기화 업로드 실패: ${addedEvent.name}', tag: _tag, error: e);
        // 실시간 동기화 실패해도 로컬 추가는 유지
      }

      LoggerUtils.logInfo('행사 추가 성공: ${addedEvent.name} (ID: ${addedEvent.id})', tag: _tag);
      LoggerUtils.methodEnd('addEvent', tag: _tag);

      return addedEvent; // 생성된 행사 반환
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 추가 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'name': event.name},
      );

      state = state.copyWith(
        isLoading: false,
        errorMessage: '행사 추가에 실패했습니다: ${e.toString()}',
      );

      return null; // 실패 시 null 반환
    }
  }

  /// 새로 생성된 행사에 닉네임 기반 판매자 자동 생성
  Future<void> _createNicknameBasedSeller(Event event) async {
    try {
      LoggerUtils.logInfo('새 행사에 닉네임 기반 판매자 자동 생성 시작: ${event.name} (ID: ${event.id})', tag: _tag);
      
      // 닉네임 정보 가져오기
      final nickname = _ref.read(nicknameProvider);
      if (nickname == null) {
        LoggerUtils.logInfo('닉네임이 없어 판매자 자동 생성을 건너뜁니다', tag: _tag);
        return;
      }

      // 닉네임 기반 판매자 생성
      final newSeller = Seller.create(
        name: nickname.name,
        isDefault: true,
        eventId: event.id!,
      );

      // 판매자 저장
      final sellerRepository = _ref.read(sellerRepositoryProvider);
      final sellerId = await sellerRepository.insertSeller(newSeller);
      
      LoggerUtils.logInfo('닉네임 기반 판매자 자동 생성 완료: ${nickname.name} (ID: $sellerId)', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '닉네임 기반 판매자 자동 생성 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'eventId': event.id, 'eventName': event.name},
      );
    }
  }

  /// 행사 정보를 업데이트합니다.
  Future<void> updateEvent(Event event) async {
    try {
      LoggerUtils.methodStart('updateEvent', tag: _tag, data: {'id': event.id, 'name': event.name});
      
      state = state.copyWith(isLoading: true, errorMessage: null);
      
      final updatedEvent = await _repository.updateEvent(event);
      
      // 상태 업데이트
      final updatedEvents = state.events.map((e) {
        return e.id == updatedEvent.id ? updatedEvent : e;
      }).toList();
      
      state = state.copyWith(
        events: updatedEvents,
        isLoading: false,
        lastUpdated: DateTime.now(),
        selectedEvent: state.selectedEvent?.id == updatedEvent.id ? updatedEvent : state.selectedEvent,
      );
      
      // 현재 선택된 행사가 업데이트된 경우 WorkspaceProvider도 업데이트
      final currentWorkspace = _ref.read(currentWorkspaceProvider);
      if (currentWorkspace?.id == updatedEvent.id) {
        final updatedWorkspace = EventWorkspaceUtils.eventToWorkspace(updatedEvent);
        if (updatedWorkspace != null) {
          await _ref.read(unifiedWorkspaceProvider.notifier).switchToWorkspace(updatedWorkspace);
        }
      }

      // 실시간 동기화: 업데이트된 행사를 Firebase에 즉시 업로드
      try {
        final dataSyncService = _ref.read(dataSyncServiceProvider);
        await dataSyncService.uploadSingleEvent(updatedEvent);
        LoggerUtils.logInfo('행사 업데이트 실시간 동기화 업로드 완료: ${updatedEvent.name}', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('행사 업데이트 실시간 동기화 업로드 실패: ${updatedEvent.name}', tag: _tag, error: e);
        // 실시간 동기화 실패해도 로컬 업데이트는 유지
      }
      
      LoggerUtils.logInfo('행사 업데이트 성공: ${updatedEvent.name}', tag: _tag);
      LoggerUtils.methodEnd('updateEvent', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 업데이트 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'id': event.id, 'name': event.name},
      );
      
      state = state.copyWith(
        isLoading: false,
        errorMessage: '행사 업데이트에 실패했습니다: ${e.toString()}',
      );
    }
  }

  /// 행사를 삭제합니다.
  Future<void> deleteEvent(int id) async {
    try {
      LoggerUtils.methodStart('deleteEvent', tag: _tag, data: {'id': id});
      
      state = state.copyWith(isLoading: true, errorMessage: null);

      // 삭제할 행사 정보 미리 저장 (실시간 동기화용)
      final eventToDelete = state.events.firstWhere((e) => e.id == id);
      
      await _repository.deleteEvent(id);
      
      // 상태 업데이트
      final updatedEvents = state.events.where((e) => e.id != id).toList();
      state = state.copyWith(
        events: updatedEvents,
        isLoading: false,
        lastUpdated: DateTime.now(),
        totalCount: updatedEvents.length,
        filteredCount: updatedEvents.length,
        selectedEvent: state.selectedEvent?.id == id ? null : state.selectedEvent,
      );
      
      // 현재 선택된 행사가 삭제된 경우 다른 행사로 변경
      final currentWorkspace = _ref.read(currentWorkspaceProvider);
      if (currentWorkspace?.id == id) {
        // 남은 행사 중 첫 번째 행사로 변경
        if (updatedEvents.isNotEmpty) {
          final firstEvent = updatedEvents.first;
          final firstWorkspace = EventWorkspaceUtils.eventToWorkspace(firstEvent);
          if (firstWorkspace != null) {
            await _ref.read(unifiedWorkspaceProvider.notifier).switchToWorkspace(firstWorkspace);
          }
        }
      }

      // 실시간 동기화: 삭제된 행사를 Firebase에서 즉시 삭제
      try {
        final dataSyncService = _ref.read(dataSyncServiceProvider);
        await dataSyncService.deleteSingleEvent(eventToDelete);
        LoggerUtils.logInfo('행사 삭제 실시간 동기화 완료: ${eventToDelete.name}', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('행사 삭제 실시간 동기화 실패: ${eventToDelete.name}', tag: _tag, error: e);
        // 실시간 동기화 실패해도 로컬 삭제는 유지
      }
      
      LoggerUtils.logInfo('행사 삭제 성공: ID $id', tag: _tag);
      LoggerUtils.methodEnd('deleteEvent', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 삭제 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'id': id},
      );
      
      state = state.copyWith(
        isLoading: false,
        errorMessage: '행사 삭제에 실패했습니다: ${e.toString()}',
      );
    }
  }

  /// 행사를 검색합니다.
  Future<void> searchEvents(String keyword) async {
    try {
      LoggerUtils.methodStart('searchEvents', tag: _tag, data: {'keyword': keyword});
      
      final filter = state.currentFilter.copyWith(searchKeyword: keyword);
      
      state = state.copyWith(
        isSearchMode: keyword.isNotEmpty,
        currentFilter: filter,
      );
      
      await loadEvents(showLoading: false, filter: filter);
      
      LoggerUtils.methodEnd('searchEvents', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 검색 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'keyword': keyword},
      );
    }
  }

  /// 필터를 적용합니다.
  Future<void> applyFilter(EventFilter filter) async {
    try {
      LoggerUtils.methodStart('applyFilter', tag: _tag);
      
      await loadEvents(showLoading: false, filter: filter);
      
      LoggerUtils.methodEnd('applyFilter', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '필터 적용 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// 정렬 옵션을 변경합니다.
  Future<void> changeSortOption(EventSortOption sortOption) async {
    final filter = state.currentFilter.copyWith(sortOption: sortOption);
    await applyFilter(filter);
  }

  /// 행사를 선택합니다.
  void selectEvent(Event? event) {
    state = state.copyWith(selectedEvent: event);
  }

  /// 현재 선택된 행사를 설정합니다.
  Future<void> setCurrentEvent(Event event) async {
    try {
      final workspace = EventWorkspaceUtils.eventToWorkspace(event);
      if (workspace != null) {
        await _ref.read(unifiedWorkspaceProvider.notifier).switchToWorkspace(workspace);
        LoggerUtils.logInfo('현재 행사를 워크스페이스로 변경: ${event.name}', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('현재 행사 설정 실패', tag: _tag, error: e);
    }
  }

  /// 에러를 클리어합니다.
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  /// 검색 모드를 해제합니다.
  Future<void> clearSearch() async {
    final filter = state.currentFilter.copyWith(searchKeyword: '');
    state = state.copyWith(isSearchMode: false);
    await applyFilter(filter);
  }

  /// 필터를 초기화합니다.
  Future<void> resetFilter() async {
    await applyFilter(EventFilter.defaultFilter);
  }

  /// 새로고침합니다.
  Future<void> refresh() async {
    await loadEvents(showLoading: false);
  }
}

/// EventProvider
final eventNotifierProvider = StateNotifierProvider<EventNotifier, EventState>((ref) {
  final repository = ref.read(eventRepositoryProvider);
  return EventNotifier(repository, ref);
});

/// 현재 행사 목록만 필요한 경우를 위한 Provider
final eventsProvider = Provider<List<Event>>((ref) {
  final eventState = ref.watch(eventNotifierProvider);
  return eventState.events;
});

/// 진행 중인 행사 목록 Provider
final ongoingEventsProvider = Provider<List<Event>>((ref) {
  final eventState = ref.watch(eventNotifierProvider);
  return eventState.ongoingEvents;
});

/// 예정된 행사 목록 Provider
final upcomingEventsProvider = Provider<List<Event>>((ref) {
  final eventState = ref.watch(eventNotifierProvider);
  return eventState.upcomingEvents;
});

/// 종료된 행사 목록 Provider
final endedEventsProvider = Provider<List<Event>>((ref) {
  final eventState = ref.watch(eventNotifierProvider);
  return eventState.endedEvents;
});

/// 행사 로딩 상태 Provider
final eventLoadingProvider = Provider<bool>((ref) {
  final eventState = ref.watch(eventNotifierProvider);
  return eventState.isLoading;
});

/// 행사 에러 상태 Provider
final eventErrorProvider = Provider<String?>((ref) {
  final eventState = ref.watch(eventNotifierProvider);
  return eventState.errorMessage;
});
