// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in parabara/test/test_helper.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:mockito/mockito.dart' as _i1;
import 'package:parabara/repositories/settings_repository.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [SettingsRepository].
///
/// See the documentation for <PERSON><PERSON><PERSON>'s code generation for more information.
class MockSettingsRepositoryForTest extends _i1.Mock
    implements _i2.SettingsRepository {
  MockSettingsRepositoryForTest() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<bool> setEventDayOfWeek(int? dayOfWeek) =>
      (super.noSuchMethod(
            Invocation.method(#setEventDayOfWeek, [dayOfWeek]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<int> getEventDayOfWeek() =>
      (super.noSuchMethod(
            Invocation.method(#getEventDayOfWeek, []),
            returnValue: _i3.Future<int>.value(0),
          )
          as _i3.Future<int>);

  @override
  _i3.Future<bool> clearSettings() =>
      (super.noSuchMethod(
            Invocation.method(#clearSettings, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<bool> remove(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#remove, [key]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<Set<String>> getKeys() =>
      (super.noSuchMethod(
            Invocation.method(#getKeys, []),
            returnValue: _i3.Future<Set<String>>.value(<String>{}),
          )
          as _i3.Future<Set<String>>);

  @override
  _i3.Future<bool> setString(String? key, String? value) =>
      (super.noSuchMethod(
            Invocation.method(#setString, [key, value]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<String?> getString(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#getString, [key]),
            returnValue: _i3.Future<String?>.value(),
          )
          as _i3.Future<String?>);

  @override
  _i3.Future<bool> setInt(String? key, int? value) =>
      (super.noSuchMethod(
            Invocation.method(#setInt, [key, value]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<int?> getInt(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#getInt, [key]),
            returnValue: _i3.Future<int?>.value(),
          )
          as _i3.Future<int?>);

  @override
  _i3.Future<bool> setBool(String? key, bool? value) =>
      (super.noSuchMethod(
            Invocation.method(#setBool, [key, value]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<bool?> getBool(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#getBool, [key]),
            returnValue: _i3.Future<bool?>.value(),
          )
          as _i3.Future<bool?>);

  @override
  _i3.Future<bool> setDouble(String? key, double? value) =>
      (super.noSuchMethod(
            Invocation.method(#setDouble, [key, value]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<double?> getDouble(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#getDouble, [key]),
            returnValue: _i3.Future<double?>.value(),
          )
          as _i3.Future<double?>);

  @override
  _i3.Future<bool> setStringList(String? key, List<String>? value) =>
      (super.noSuchMethod(
            Invocation.method(#setStringList, [key, value]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<List<String>?> getStringList(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#getStringList, [key]),
            returnValue: _i3.Future<List<String>?>.value(),
          )
          as _i3.Future<List<String>?>);

  @override
  _i3.Future<bool> setCollectDayOfWeekFromExcel(bool? value) =>
      (super.noSuchMethod(
            Invocation.method(#setCollectDayOfWeekFromExcel, [value]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<bool?> getCollectDayOfWeekFromExcel() =>
      (super.noSuchMethod(
            Invocation.method(#getCollectDayOfWeekFromExcel, []),
            returnValue: _i3.Future<bool?>.value(),
          )
          as _i3.Future<bool?>);

  @override
  _i3.Future<bool> setExcelDayOfWeekColumnIndex(int? value) =>
      (super.noSuchMethod(
            Invocation.method(#setExcelDayOfWeekColumnIndex, [value]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<int?> getExcelDayOfWeekColumnIndex() =>
      (super.noSuchMethod(
            Invocation.method(#getExcelDayOfWeekColumnIndex, []),
            returnValue: _i3.Future<int?>.value(),
          )
          as _i3.Future<int?>);

  @override
  _i3.Future<bool> setLinkPrepaymentToInventory(bool? value) =>
      (super.noSuchMethod(
            Invocation.method(#setLinkPrepaymentToInventory, [value]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<bool?> getLinkPrepaymentToInventory() =>
      (super.noSuchMethod(
            Invocation.method(#getLinkPrepaymentToInventory, []),
            returnValue: _i3.Future<bool?>.value(),
          )
          as _i3.Future<bool?>);

  @override
  _i3.Future<bool> setInventoryColumns(int? value) =>
      (super.noSuchMethod(
            Invocation.method(#setInventoryColumns, [value]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<int?> getInventoryColumns() =>
      (super.noSuchMethod(
            Invocation.method(#getInventoryColumns, []),
            returnValue: _i3.Future<int?>.value(),
          )
          as _i3.Future<int?>);

  @override
  _i3.Future<bool> setSaleColumns(int? value) =>
      (super.noSuchMethod(
            Invocation.method(#setSaleColumns, [value]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<int?> getSaleColumns() =>
      (super.noSuchMethod(
            Invocation.method(#getSaleColumns, []),
            returnValue: _i3.Future<int?>.value(),
          )
          as _i3.Future<int?>);

  @override
  _i3.Future<bool> setInventoryColumnsPortrait(int? value) =>
      (super.noSuchMethod(
            Invocation.method(#setInventoryColumnsPortrait, [value]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<int?> getInventoryColumnsPortrait() =>
      (super.noSuchMethod(
            Invocation.method(#getInventoryColumnsPortrait, []),
            returnValue: _i3.Future<int?>.value(),
          )
          as _i3.Future<int?>);

  @override
  _i3.Future<bool> setInventoryColumnsLandscape(int? value) =>
      (super.noSuchMethod(
            Invocation.method(#setInventoryColumnsLandscape, [value]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<int?> getInventoryColumnsLandscape() =>
      (super.noSuchMethod(
            Invocation.method(#getInventoryColumnsLandscape, []),
            returnValue: _i3.Future<int?>.value(),
          )
          as _i3.Future<int?>);

  @override
  _i3.Future<bool> setSaleColumnsPortrait(int? value) =>
      (super.noSuchMethod(
            Invocation.method(#setSaleColumnsPortrait, [value]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<int?> getSaleColumnsPortrait() =>
      (super.noSuchMethod(
            Invocation.method(#getSaleColumnsPortrait, []),
            returnValue: _i3.Future<int?>.value(),
          )
          as _i3.Future<int?>);

  @override
  _i3.Future<bool> setSaleColumnsLandscape(int? value) =>
      (super.noSuchMethod(
            Invocation.method(#setSaleColumnsLandscape, [value]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<int?> getSaleColumnsLandscape() =>
      (super.noSuchMethod(
            Invocation.method(#getSaleColumnsLandscape, []),
            returnValue: _i3.Future<int?>.value(),
          )
          as _i3.Future<int?>);
}
