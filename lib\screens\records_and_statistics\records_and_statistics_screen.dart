import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../models/transaction_type.dart';
import '../../providers/sales_log_provider.dart';
import '../../providers/seller_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/product_provider.dart';
import '../../providers/prepayment_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../models/sales_log.dart';
import '../../models/prepayment.dart';
import '../../utils/toast_utils.dart';
import '../../widgets/sales_record_filter_dialog.dart';
import '../../widgets/statistics_filter_dialog.dart';
import '../../widgets/excel_preview_dialog.dart';
import '../../widgets/pdf_preview_dialog.dart';
import '../../widgets/pdf_export_type_selection_dialog.dart';
import '../../services/pdf_export_service.dart';
import '../sales_log/sales_log_list_tab.dart';
import 'statistics_tab_content.dart';
import '../../utils/app_colors.dart';

/// 기록&통계 화면
///
/// 판매 기록과 통계를 하나의 화면에서 관리하는 2탭 구조의 화면입니다.
/// - 좌측 탭: 판매 기록 (기존 SalesLogListTab 기반)
/// - 우측 탭: 통계 (기존 StatisticsScreen 기반)
/// - 통합된 필터 다이얼로그 제공
/// - 슬라이드 탭 전환 지원
class RecordsAndStatisticsScreen extends ConsumerStatefulWidget {
  final int initialTabIndex;

  const RecordsAndStatisticsScreen({
    super.key,
    this.initialTabIndex = 0,
  });

  @override
  ConsumerState<RecordsAndStatisticsScreen> createState() => _RecordsAndStatisticsScreenState();
}

class _RecordsAndStatisticsScreenState extends ConsumerState<RecordsAndStatisticsScreen>
    with SingleTickerProviderStateMixin, RestorationMixin {
  late TabController _tabController;

  // 차트 캡처용 GlobalKey
  final GlobalKey _chartKey = GlobalKey();

  // 필터 상태
  String _selectedSeller = '전체 판매자';
  DateTimeRange? _selectedDateRange;
  TransactionType? _selectedTransactionType;
  String _selectedSortOption = '최신순'; // 판매기록용 정렬 옵션

  @override
  String? get restorationId => 'records_and_statistics_screen';
  
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: widget.initialTabIndex,
    );
    
    // 탭 변경 시 앱바 다시 빌드하기 위해 리스너 추가
    _tabController.addListener(() {
      if (mounted) {
        setState(() {
          // 탭 변경 시 State 업데이트
        });
      }
    });

    // 데이터 로드 (타임아웃 안전장치 포함)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDataWithTimeout();
    });
  }

  /// 타임아웃 안전장치가 포함된 데이터 로딩
  Future<void> _loadDataWithTimeout() async {
    try {
      // 15초 타임아웃 설정 (카테고리/상품 로딩 포함)
      await Future.wait([
        ref.read(salesLogNotifierProvider.notifier).loadSalesLogs(),
        ref.read(sellerNotifierProvider.notifier).loadSellers(),
        ref.read(categoryNotifierProvider.notifier).loadCategories(),
        ref.read(productNotifierProvider.notifier).loadProducts(),
      ]).timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          // 타임아웃 발생 시 모든 로딩 상태 강제 해제
          if (mounted) {
            final salesLogNotifier = ref.read(salesLogNotifierProvider.notifier);
            final sellerNotifier = ref.read(sellerNotifierProvider.notifier);
            final categoryNotifier = ref.read(categoryNotifierProvider.notifier);
            final productNotifier = ref.read(productNotifierProvider.notifier);

            // 강제로 로딩 상태 해제
            salesLogNotifier.state = salesLogNotifier.state.copyWith(isLoading: false);
            sellerNotifier.state = sellerNotifier.state.copyWith(isLoading: false);
            categoryNotifier.state = const AsyncValue.data([]);
            productNotifier.state = productNotifier.state.copyWith(isLoading: false);
          }
          throw TimeoutException('데이터 로딩 타임아웃', const Duration(seconds: 15));
        },
      );
    } catch (e) {
      // 오류 발생 시에도 모든 로딩 상태 해제
      if (mounted) {
        final salesLogNotifier = ref.read(salesLogNotifierProvider.notifier);
        final sellerNotifier = ref.read(sellerNotifierProvider.notifier);
        final categoryNotifier = ref.read(categoryNotifierProvider.notifier);
        final productNotifier = ref.read(productNotifierProvider.notifier);

        salesLogNotifier.state = salesLogNotifier.state.copyWith(isLoading: false);
        sellerNotifier.state = sellerNotifier.state.copyWith(isLoading: false);
        categoryNotifier.state = const AsyncValue.data([]);
        productNotifier.state = productNotifier.state.copyWith(isLoading: false);
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 화면이 다시 포커스될 때 데이터 새로고침
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          '기록&통계',
          style: Theme.of(context).textTheme.titleLarge!.copyWith(
            fontFamily: 'Pretendard',
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        surfaceTintColor: Colors.transparent, // Material 3에서 색상 덮어쓰기 방지
        elevation: 0,
        scrolledUnderElevation: 0,
        actions: [
          // 다운로드 버튼 (통계 탭에서만 표시)
          if (_tabController.index == 1)
            PopupMenuButton<String>(
              icon: const Icon(Icons.download),
              tooltip: '내보내기',
              onSelected: (value) {
                if (value == 'excel') {
                  _exportToExcel();
                } else if (value == 'pdf') {
                  _exportToPdf();
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem<String>(
                  value: 'excel',
                  child: Row(
                    children: [
                      Icon(Icons.table_chart, color: Colors.green, size: 20),
                      SizedBox(width: 8),
                      Text('엑셀로 내보내기'),
                    ],
                  ),
                ),
                const PopupMenuItem<String>(
                  value: 'pdf',
                  child: Row(
                    children: [
                      Icon(Icons.picture_as_pdf, color: Colors.red, size: 20),
                      SizedBox(width: 8),
                      Text('PDF로 내보내기'),
                    ],
                  ),
                ),
              ],
            ),
          // 필터 버튼
          IconButton(
            icon: const Icon(Icons.sort),
            onPressed: _showFilterDialog,
            tooltip: '정렬 및 필터',
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 모던한 탭 바
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TabBar(
                controller: _tabController,
                labelColor: Theme.of(context).colorScheme.primary,
                unselectedLabelColor: Colors.grey.shade600,
                indicatorColor: Theme.of(context).colorScheme.primary,
                indicatorWeight: 3,
                indicatorSize: TabBarIndicatorSize.label,
                labelStyle: const TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                unselectedLabelStyle: const TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                tabs: const [
                  Tab(text: '판매 기록'),
                  Tab(text: '통계'),
                ],
              ),
            ),
            // 탭 뷰
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
            // 판매 기록 탭
            SalesLogListTab(
              selectedSeller: _selectedSeller,
              selectedDateRange: _selectedDateRange,
              selectedTransactionType: _selectedTransactionType,
              selectedSortOption: _selectedSortOption,
            ),
            // 통계 탭 (새로운 StatisticsTabContent 사용)
            StatisticsTabContent(
              selectedSeller: _selectedSeller,
              selectedDateRange: _selectedDateRange,
              selectedTransactionType: _selectedTransactionType,
              chartKey: _chartKey, // 차트 키 전달
            ),
          ],
        ),
      ),
        ],
      ),
    ),
    );
  }

  /// 필터 다이얼로그 표시 (현재 탭에 따라 다른 다이얼로그 사용)
  Future<void> _showFilterDialog() async {
    final currentTabIndex = _tabController.index;

    if (currentTabIndex == 0) {
      // 판매기록 탭: 정렬 + 거래유형 + 판매자 + 기간
      await _showSalesRecordFilterDialog();
    } else {
      // 통계 탭: 판매자 + 기간만
      await _showStatisticsFilterDialog();
    }
  }

  /// 판매기록용 필터 다이얼로그 표시
  Future<void> _showSalesRecordFilterDialog() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => SalesRecordFilterDialog(
        initialSeller: _selectedSeller,
        initialDateRange: _selectedDateRange,
        initialTransactionType: _selectedTransactionType,
        initialSortOption: _selectedSortOption,
      ),
    );

    if (result != null) {
      setState(() {
        _selectedSeller = result['seller'] ?? '전체 판매자';
        _selectedDateRange = result['dateRange'];
        _selectedTransactionType = result['transactionType'];
        _selectedSortOption = result['sortOption'] ?? '최신순';
      });
    }
  }

  /// 통계용 필터 다이얼로그 표시
  Future<void> _showStatisticsFilterDialog() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => StatisticsFilterDialog(
        initialSeller: _selectedSeller,
        initialDateRange: _selectedDateRange,
      ),
    );

    if (result != null) {
      setState(() {
        _selectedSeller = result['seller'] ?? '전체 판매자';
        _selectedDateRange = result['dateRange'];
        // 통계 탭에서는 거래유형과 정렬 옵션을 초기화하지 않음
      });
    }
  }

  /// 엑셀로 내보내기 (미리보기 다이얼로그 표시)
  Future<void> _exportToExcel() async {
    try {
      // 통계 데이터 계산을 위해 필요한 데이터들 가져오기
      final salesLogState = ref.read(salesLogNotifierProvider);
      final prepaymentState = ref.read(prepaymentNotifierProvider);
      final productState = ref.read(productNotifierProvider);
      final categories = ref.read(currentCategoriesProvider);
      
      // 필터링된 데이터 계산 (StatisticsTabContent와 동일한 로직)
      final filteredSalesLogs = _getFilteredSalesLogs(salesLogState.salesLogs);
      final filteredPrepayments = _getFilteredPrepayments(prepaymentState.prepayments);
      
      // productId -> categoryName 매핑 생성
      Map<int, String>? productCategoryMap;
      if (productState.products.isNotEmpty && categories.isNotEmpty) {
        productCategoryMap = <int, String>{};
        for (final product in productState.products) {
          if (product.id != null) {
            final matched = categories.where((c) => c.id == product.categoryId);
            if (matched.isNotEmpty) {
              productCategoryMap[product.id!] = matched.first.name;
            }
          }
        }
      }
      
      // 통계 데이터 계산
      final statsData = _calculateStatistics(filteredSalesLogs, filteredPrepayments, productCategoryMap: productCategoryMap);
      
      // 미리보기 다이얼로그 표시
      if (!mounted) return;
      await showDialog(
        context: context,
        builder: (context) => ExcelPreviewDialog(
          statsData: statsData,
          seller: _selectedSeller != '전체 판매자' ? _selectedSeller : null,
          dateRange: _selectedDateRange,
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ToastUtils.showError(context, '데이터 준비 중 오류가 발생했습니다: $e');
    }
  }

  /// 필터링된 판매 기록 반환 (StatisticsTabContent와 동일한 로직)
  List<SalesLog> _getFilteredSalesLogs(List<SalesLog> allLogs) {
    return allLogs.where((log) {
      // 판매자 필터
      if (_selectedSeller != '전체 판매자' &&
          (log.sellerName ?? '알 수 없음') != _selectedSeller) {
        return false;
      }

      // 날짜 범위 필터
      if (_selectedDateRange != null) {
        final logDate = DateTime.fromMillisecondsSinceEpoch(log.saleTimestamp);
        final startDate = _selectedDateRange!.start;
        final endDate = _selectedDateRange!.end.add(const Duration(days: 1));

        if (logDate.isBefore(startDate) || logDate.isAfter(endDate)) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  /// 필터링된 선입금 기록 반환 (StatisticsTabContent와 동일한 로직)
  List<Prepayment> _getFilteredPrepayments(List<Prepayment> allPrepayments) {
    return allPrepayments.where((prepayment) {
      // 날짜 범위 필터
      if (_selectedDateRange != null) {
        final prepaymentDate = prepayment.registrationDate;
        final startDate = _selectedDateRange!.start;
        final endDate = _selectedDateRange!.end.add(const Duration(days: 1));

        if (prepaymentDate.isBefore(startDate) || prepaymentDate.isAfter(endDate)) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  /// 통계 데이터 계산 (StatisticsTabContent와 동일한 로직을 단순화)
  Map<String, dynamic> _calculateStatistics(List<SalesLog> logs, List<Prepayment> prepayments, {Map<int, String>? productCategoryMap}) {
    if (logs.isEmpty && prepayments.isEmpty) {
      return _getEmptyStats();
    }

    // 기본 매출 통계
    final totalRevenue = logs.fold<int>(0, (sum, log) => sum + log.totalAmount);

    // 실제 거래 건수 계산 (batchSaleId로 그룹핑)
    final uniqueTransactions = <String>{};
    for (final log in logs) {
      if (log.batchSaleId != null) {
        uniqueTransactions.add(log.batchSaleId!);
      } else {
        uniqueTransactions.add('single_${log.id}_${log.saleTimestamp}');
      }
    }
    final totalTransactions = uniqueTransactions.length;

    final averageTransaction = totalTransactions > 0 ? totalRevenue ~/ totalTransactions : 0;
    final totalQuantity = logs.fold<int>(0, (sum, log) => sum + log.soldQuantity);

    // 상품별 분석
    final productStats = <String, Map<String, int>>{};
    for (final log in logs) {
      String productDisplayName = log.productName;
      
      if (log.productId != null && productCategoryMap != null) {
        final categoryName = productCategoryMap[log.productId];
        if (categoryName != null) {
          productDisplayName = '$categoryName-${log.productName}';
        }
      }

      if (!productStats.containsKey(productDisplayName)) {
        productStats[productDisplayName] = {'quantity': 0, 'revenue': 0};
      }
      final currentQuantity = productStats[productDisplayName]!['quantity'] ?? 0;
      final currentRevenue = productStats[productDisplayName]!['revenue'] ?? 0;
      productStats[productDisplayName]!['quantity'] = currentQuantity + log.soldQuantity;
      productStats[productDisplayName]!['revenue'] = currentRevenue + log.totalAmount;
    }

    // 카테고리별 분석
    final categoryStats = <String, Map<String, int>>{};
    for (final log in logs) {
      String categoryName = '기타';

      if (log.productId != null && productCategoryMap != null) {
        categoryName = productCategoryMap[log.productId] ?? '기타';
      }

      if (!categoryStats.containsKey(categoryName)) {
        categoryStats[categoryName] = {'quantity': 0, 'revenue': 0};
      }
      final currentQuantity = categoryStats[categoryName]!['quantity'] ?? 0;
      final currentRevenue = categoryStats[categoryName]!['revenue'] ?? 0;
      categoryStats[categoryName]!['quantity'] = currentQuantity + log.soldQuantity;
      categoryStats[categoryName]!['revenue'] = currentRevenue + log.totalAmount;
    }

    // 판매자별 성과
    final sellerStats = <String, Map<String, int>>{};
    final sellerTransactions = <String, Set<String>>{};

    for (final log in logs) {
      final seller = log.sellerName ?? '알 수 없음';
      if (!sellerStats.containsKey(seller)) {
        sellerStats[seller] = {'count': 0, 'revenue': 0};
        sellerTransactions[seller] = <String>{};
      }

      final transactionId = log.batchSaleId ?? 'single_${log.id}_${log.saleTimestamp}';
      sellerTransactions[seller]!.add(transactionId);

      final currentRevenue = sellerStats[seller]!['revenue'] ?? 0;
      sellerStats[seller]!['revenue'] = currentRevenue + log.totalAmount;
    }

    for (final seller in sellerStats.keys) {
      sellerStats[seller]!['count'] = sellerTransactions[seller]!.length;
    }

    // 선입금 분석
    final totalPrepaymentAmount = prepayments.fold<int>(0, (sum, p) => sum + p.amount);
    final receivedPrepayments = prepayments.where((p) => p.isReceived).toList();
    final receivedPrepaymentAmount = receivedPrepayments.fold<int>(0, (sum, p) => sum + p.amount);
    final pendingPrepaymentAmount = totalPrepaymentAmount - receivedPrepaymentAmount;

    // 할인 분석
    final totalSetDiscountAmount = logs.fold<int>(0, (sum, log) => sum + log.setDiscountAmount);
    final setDiscountCount = logs.where((log) => log.setDiscountAmount > 0).length;
    final totalManualDiscountAmount = logs.fold<int>(0, (sum, log) => sum + log.manualDiscountAmount);
    final manualDiscountCount = logs.where((log) => log.manualDiscountAmount > 0).length;

    // 서비스 분석 (soldPrice == 0인 거래)
    final serviceLogs = logs.where((log) => log.soldPrice == 0).toList();
    final serviceProductTypes = serviceLogs.map((log) => log.productName).toSet().length;
    final serviceCount = serviceLogs.length;
    final totalServiceQuantity = serviceLogs.fold<int>(0, (sum, log) => sum + log.soldQuantity);

    // 세트 판매 분석 (상세 데이터 포함)
    final setDiscountStats = <String, Map<String, int>>{};
    final setDiscountLogs = logs.where((log) => log.setDiscountNames != null && log.setDiscountNames!.isNotEmpty).toList();

    print('🔍 세트 분석 디버깅:');
    print('  전체 판매기록: ${logs.length}개');
    print('  세트 할인 적용 기록: ${setDiscountLogs.length}개');

    for (final log in setDiscountLogs) {
      print('  세트 할인 기록: ${log.setDiscountNames} (할인액: ${log.setDiscountAmount})');
      final setNames = log.setDiscountNames!.split(',').map((name) => name.trim()).toList();
      for (final setName in setNames) {
        if (!setDiscountStats.containsKey(setName)) {
          setDiscountStats[setName] = {'count': 0, 'totalDiscount': 0};
        }
        setDiscountStats[setName]!['count'] = (setDiscountStats[setName]!['count'] ?? 0) + 1;
        // 여러 세트가 적용된 경우 할인액을 균등 분배
        final discountPerSet = setNames.length > 1 ? (log.setDiscountAmount / setNames.length).round() : log.setDiscountAmount;
        setDiscountStats[setName]!['totalDiscount'] = (setDiscountStats[setName]!['totalDiscount'] ?? 0) + discountPerSet;
      }
    }

    print('  계산된 세트 통계: $setDiscountStats');

    // 세트별 상세 내역 생성 (다이얼로그용)
    final setDiscountDetails = setDiscountStats.entries.map((entry) {
      return {
        'setName': entry.key,
        'appliedCount': entry.value['count'] ?? 0,
        'totalDiscount': entry.value['totalDiscount'] ?? 0,
      };
    }).toList();

    // 할인액 기준으로 정렬 (높은 순)
    setDiscountDetails.sort((a, b) => (b['totalDiscount'] as int).compareTo(a['totalDiscount'] as int));

    print('  생성된 세트 상세 내역: ${setDiscountDetails.length}개');
    for (final detail in setDiscountDetails) {
      print('    ${detail['setName']}: ${detail['appliedCount']}회, ${detail['totalDiscount']}원');
    }

    return {
      'totalRevenue': totalRevenue,
      'totalTransactions': totalTransactions,
      'averageTransaction': averageTransaction,
      'totalQuantity': totalQuantity,
      'totalSetDiscountAmount': totalSetDiscountAmount,
      'setDiscountCount': setDiscountCount,
      'totalManualDiscountAmount': totalManualDiscountAmount,
      'manualDiscountCount': manualDiscountCount,
      'serviceProductTypes': serviceProductTypes,
      'serviceCount': serviceCount,
      'totalServiceQuantity': totalServiceQuantity,
      'setDiscountStats': setDiscountStats,
      'setDiscountDetails': setDiscountDetails, // 세트별 상세 내역 추가
      'productStats': productStats,
      'categoryStats': categoryStats,
      'sellerStats': sellerStats,
      'totalPrepaymentAmount': totalPrepaymentAmount,
      'receivedPrepaymentAmount': receivedPrepaymentAmount,
      'pendingPrepaymentAmount': pendingPrepaymentAmount,
      'totalPrepaymentCount': prepayments.length,
      'receivedPrepaymentCount': receivedPrepayments.length,
      'pendingPrepaymentCount': prepayments.length - receivedPrepayments.length,
    };
  }

  /// PDF로 내보내기
  Future<void> _exportToPdf() async {
    try {
      // 통계 데이터 계산을 위해 필요한 데이터들 가져오기
      final salesLogState = ref.read(salesLogNotifierProvider);
      final prepaymentState = ref.read(prepaymentNotifierProvider);
      final productState = ref.read(productNotifierProvider);
      final categories = ref.read(currentCategoriesProvider);
      
      // 필터링된 데이터 계산
      final filteredSalesLogs = _getFilteredSalesLogs(salesLogState.salesLogs);
      final filteredPrepayments = _getFilteredPrepayments(prepaymentState.prepayments);
      
      // productId -> categoryName 매핑 생성
      Map<int, String>? productCategoryMap;
      if (productState.products.isNotEmpty && categories.isNotEmpty) {
        productCategoryMap = <int, String>{};
        for (final product in productState.products) {
          if (product.id != null) {
            final matched = categories.where((c) => c.id == product.categoryId);
            if (matched.isNotEmpty) {
              productCategoryMap[product.id!] = matched.first.name;
            }
          }
        }
      }

      // 통계 데이터 계산
      final statsData = _calculateStatistics(filteredSalesLogs, filteredPrepayments, productCategoryMap: productCategoryMap);

      // 일별 통계 계산
      final dailyStats = <DateTime, Map<String, dynamic>>{};
      for (final log in filteredSalesLogs) {
        final dateTime = DateTime.fromMillisecondsSinceEpoch(log.saleTimestamp);
        final date = DateTime(dateTime.year, dateTime.month, dateTime.day);
        if (!dailyStats.containsKey(date)) {
          dailyStats[date] = {'totalRevenue': 0, 'totalQuantity': 0};
        }
        final currentRevenue = dailyStats[date]!['totalRevenue'] as int? ?? 0;
        final currentQuantity = dailyStats[date]!['totalQuantity'] as int? ?? 0;
        dailyStats[date]!['totalRevenue'] = currentRevenue + log.soldPrice;
        dailyStats[date]!['totalQuantity'] = currentQuantity + log.soldQuantity;
      }

      // 상품별 통계 리스트 변환
      final productStatsList = (statsData['productStats'] as Map<String, Map<String, dynamic>>?)
          ?.entries
          .map((entry) => {
                'productName': entry.key,
                'totalRevenue': entry.value['revenue'] ?? 0,
                'totalQuantity': entry.value['quantity'] ?? 0,
                'revenueShare': entry.value['revenueShare'] ?? 0.0,
              })
          .toList() ?? <Map<String, dynamic>>[];

      // 매출 순으로 정렬
      productStatsList.sort((a, b) {
        final revenueA = a['totalRevenue'] as int? ?? 0;
        final revenueB = b['totalRevenue'] as int? ?? 0;
        return revenueB.compareTo(revenueA);
      });

      // PDF 내보내기 타입 선택 다이얼로그 표시
      if (mounted) {
        final selectedType = await showDialog<PdfExportType>(
          context: context,
          builder: (context) => const PdfExportTypeSelectionDialog(),
        );

        // 사용자가 타입을 선택했다면 PDF 미리보기 다이얼로그 표시
        if (selectedType != null && mounted) {
          // 현재 워크스페이스(이벤트) 이름 가져오기
          final eventName = ref.read(currentWorkspaceNameProvider);
          
          showDialog<void>(
            context: context,
            builder: (context) => PdfPreviewDialog(
              dailyStats: dailyStats,
              productStats: productStatsList,
              fullStats: statsData, // 전체 통계 데이터 추가
              dateRange: _selectedDateRange,
              eventName: eventName.isNotEmpty ? eventName : '바라 부스 매니저 통계',
              chartKeys: {'main': _chartKey}, // 다중 차트 키 지원
              exportType: selectedType, // 선택된 타입 전달
            ),
          );
        }
      }
    } catch (e) {
      if (!mounted) return;
      ToastUtils.showError(context, 'PDF 생성 중 오류가 발생했습니다: $e');
    }
  }

  /// 빈 통계 데이터 반환
  Map<String, dynamic> _getEmptyStats() {
    return {
      'totalRevenue': 0,
      'totalTransactions': 0,
      'averageTransaction': 0,
      'totalQuantity': 0,
      'totalSetDiscountAmount': 0,
      'setDiscountCount': 0,
      'totalManualDiscountAmount': 0,
      'manualDiscountCount': 0,
      'serviceProductTypes': 0,
      'serviceCount': 0,
      'totalServiceQuantity': 0,
      'setDiscountStats': <String, Map<String, int>>{},
      'setDiscountDetails': <Map<String, dynamic>>[], // 빈 세트별 상세 내역
      'productStats': <String, Map<String, int>>{},
      'categoryStats': <String, Map<String, int>>{},
      'sellerStats': <String, Map<String, int>>{},
      'totalPrepaymentAmount': 0,
      'receivedPrepaymentAmount': 0,
      'pendingPrepaymentAmount': 0,
      'totalPrepaymentCount': 0,
      'receivedPrepaymentCount': 0,
      'pendingPrepaymentCount': 0,
    };
  }
}


