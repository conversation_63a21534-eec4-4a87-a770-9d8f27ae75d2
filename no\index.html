<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>바라 부스 매니저 - Parabara</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.7;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 2px solid #f1f3f4;
        }
        
        .logo {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 25px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 36px;
            font-weight: bold;
        }
        
        h1 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 36px;
            font-weight: 700;
        }
        
        .subtitle {
            color: #7f8c8d;
            font-size: 18px;
            margin-bottom: 10px;
        }
        
        .description {
            color: #95a5a6;
            font-size: 16px;
            margin-bottom: 30px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border-left: 4px solid #667eea;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .feature-card h3 {
            color: #2c3e50;
            font-size: 20px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .feature-card p {
            color: #555;
            line-height: 1.6;
        }
        
        .download-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            margin: 40px 0;
        }
        
        .download-section h2 {
            font-size: 28px;
            margin-bottom: 15px;
        }
        
        .download-section p {
            font-size: 16px;
            margin-bottom: 25px;
            opacity: 0.9;
        }
        
        .download-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .download-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 15px 30px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            transition: background 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .download-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .links-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .link-card {
            background: #fdfdfd;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: border-color 0.3s ease;
        }
        
        .link-card:hover {
            border-color: #667eea;
        }
        
        .link-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .link-card a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }
        
        .link-card a:hover {
            text-decoration: underline;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #e9ecef;
            color: #7f8c8d;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            h1 {
                font-size: 28px;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .download-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .links-section {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">P</div>
            <h1>바라 부스 매니저</h1>
            <p class="subtitle">동인 부스 관리의 새로운 기준</p>
            <p class="description">효율적이고 스마트한 부스 운영을 위한 올인원 솔루션</p>
        </div>

        <div class="features">
            <div class="feature-card">
                <h3>📊 매출 관리</h3>
                <p>실시간 매출 현황 추적과 상세한 분석 리포트를 통해 부스 운영 성과를 한눈에 파악하세요.</p>
            </div>
            
            <div class="feature-card">
                <h3>📦 재고 관리</h3>
                <p>상품별 재고 현황을 실시간으로 관리하고, 부족한 상품에 대한 알림을 받아보세요.</p>
            </div>
            
            <div class="feature-card">
                <h3>👥 고객 관리</h3>
                <p>고객 정보와 구매 이력을 체계적으로 관리하여 더 나은 서비스를 제공하세요.</p>
            </div>
            
            <div class="feature-card">
                <h3>📱 모바일 최적화</h3>
                <p>언제 어디서나 스마트폰으로 부스 현황을 확인하고 관리할 수 있습니다.</p>
            </div>
        </div>

        <div class="download-section">
            <h2>📱 앱 다운로드</h2>
            <p>바라 부스 매니저를 지금 다운로드하고 스마트한 부스 관리를 시작하세요!</p>
            <div class="download-buttons">
                <a href="#" class="download-btn">🍎 App Store</a>
                <a href="#" class="download-btn">🤖 Google Play</a>
            </div>
        </div>

        <div class="links-section">
            <div class="link-card">
                <h3>📋 개인정보 처리방침</h3>
                <a href="/privacy-policy.html">개인정보 처리방침 보기</a>
            </div>
            
            <div class="link-card">
                <h3>🗑️ 계정 삭제</h3>
                <a href="/account-deletion.html">계정 삭제 요청</a>
            </div>
            
            <div class="link-card">
                <h3>🎧 고객 지원</h3>
                <a href="/support.html">고객 지원 센터</a>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2025 바라 부스 매니저 (Parabara). 개발자: 권태영</p>
            <p>문의: <EMAIL></p>
        </div>
    </div>
</body>
</html>
